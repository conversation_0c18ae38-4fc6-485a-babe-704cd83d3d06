"""
Configuration settings for the backend
"""

import os
from dotenv import load_dotenv

load_dotenv()

# Try to import face_recognition to check if it's available
try:
    import face_recognition
    FACE_RECOGNITION_AVAILABLE = True
    print("✅ face_recognition library is available")
except ImportError:
    FACE_RECOGNITION_AVAILABLE = False
    print("⚠️  face_recognition library not available, using OpenCV fallback")

# Try to import pyaudio to check if it's available
try:
    import pyaudio
    PYAUDIO_AVAILABLE = True
    print("✅ PyAudio is available")
except ImportError:
    PYAUDIO_AVAILABLE = False
    print("⚠️  PyAudio not available, some audio features may be limited")

# Database settings
DATABASE_URL = os.getenv("DATABASE_URL", "sqlite:///./biometric_auth.db")

# API settings
API_HOST = os.getenv("API_HOST", "0.0.0.0")
API_PORT = int(os.getenv("API_PORT", "8000"))
DEBUG = os.getenv("DEBUG", "True").lower() == "true"

# Security settings
SECRET_KEY = os.getenv("SECRET_KEY", "your-secret-key-change-this-in-production")
ALGORITHM = os.getenv("ALGORITHM", "HS256")
ACCESS_TOKEN_EXPIRE_MINUTES = int(os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", "30"))

# Recognition thresholds
FACE_RECOGNITION_THRESHOLD = float(os.getenv("FACE_RECOGNITION_THRESHOLD", "0.6"))
VOICE_RECOGNITION_THRESHOLD = float(os.getenv("VOICE_RECOGNITION_THRESHOLD", "0.6"))

# CORS settings
ALLOWED_ORIGINS = os.getenv("ALLOWED_ORIGINS", "http://localhost:5173,http://localhost:3000,http://127.0.0.1:5173").split(",")

# Feature flags
USE_FACE_RECOGNITION_LIB = FACE_RECOGNITION_AVAILABLE and os.getenv("USE_FACE_RECOGNITION_LIB", "true").lower() == "true"
USE_PYAUDIO = PYAUDIO_AVAILABLE and os.getenv("USE_PYAUDIO", "true").lower() == "true"

print(f"🔧 Configuration loaded:")
print(f"   - Face recognition: {'face_recognition lib' if USE_FACE_RECOGNITION_LIB else 'OpenCV fallback'}")
print(f"   - Audio: {'PyAudio' if USE_PYAUDIO else 'Limited audio support'}")
print(f"   - Database: {DATABASE_URL}")
print(f"   - Debug mode: {DEBUG}")
