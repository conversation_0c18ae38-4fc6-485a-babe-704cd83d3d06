from fastapi import APIRouter, HTTPException, Request
from pydantic import BaseModel
from app.database import database
from app.models import users, authentication_logs
from sqlalchemy import insert, select
from datetime import datetime
from typing import Optional

router = APIRouter()  # ✅ This is correct and essential!

# -------------------------
# Pydantic Models
# -------------------------
class UserRegister(BaseModel):
    username: str
    email: Optional[str] = None

class UserLogin(BaseModel):
    username: str

# -------------------------
# Routes
# -------------------------

@router.post("/register")
async def register_user(user_data: UserRegister):
    """Register a new user"""
    query = select(users).where(users.c.username == user_data.username)
    existing_user = await database.fetch_one(query)

    if existing_user:
        raise HTTPException(status_code=400, detail="Username already registered")

    query = insert(users).values(
        username=user_data.username,
        email=user_data.email,
        is_active=True,
        created_at=datetime.utcnow(),
        updated_at=datetime.utcnow()
    )
    user_id = await database.execute(query)

    return {
        "success": True,
        "message": "User registered successfully",
        "user_id": user_id,
        "username": user_data.username
    }

@router.post("/login")
async def login_user(user_data: UserLogin):
    """Log in a user if they exist and are active"""
    query = select(users).where(users.c.username == user_data.username)
    user = await database.fetch_one(query)

    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    if not user["is_active"]:
        raise HTTPException(status_code=400, detail="User account is inactive")

    return {
        "success": True,
        "message": "Login successful",
        "user_id": user["id"],
        "username": user["username"],
        "has_face": bool(user["face_encoding"]),
        "has_voice": bool(user["voice_print"])
    }

@router.get("/user/{username}")
async def get_user(username: str):
    """Fetch user details by username"""
    query = select(users).where(users.c.username == username)
    user = await database.fetch_one(query)

    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    return {
        "user_id": user["id"],
        "username": user["username"],
        "email": user["email"],
        "is_active": user["is_active"],
        "has_face": bool(user["face_encoding"]),
        "has_voice": bool(user["voice_print"]),
        "created_at": user["created_at"]
    }

# -------------------------
# Authentication Logging
# -------------------------

async def log_authentication(user_id, username, auth_type, success, confidence_score, ip_address, user_agent):
    """Log each authentication attempt (face/audio)"""
    query = insert(authentication_logs).values(
        user_id=user_id,
        username=username,
        auth_type=auth_type,
        success=success,
        confidence_score=str(round(confidence_score, 2)),
        ip_address=ip_address,
        user_agent=user_agent,
        timestamp=datetime.utcnow()
    )
    await database.execute(query)
