from fastapi import APIRouter, HTTPException, Request
from pydantic import BaseModel
from app.database import database
from app.models import users, authentication_logs
from sqlalchemy import insert, select
from datetime import datetime
from typing import Optional

router = APIRouter()  # ✅ This is correct and essential!

# -------------------------
# Pydantic Models
# -------------------------
class UserRegister(BaseModel):
    username: str
    email: Optional[str] = None

class UserLogin(BaseModel):
    username: str

# -------------------------
# Routes
# -------------------------

@router.post("/register")
async def register_user(user_data: UserRegister):
    """Register a new user - Only for authorized team members"""

    # Define authorized team members
    AUTHORIZED_USERS = [
        "fenny_mary",
        "george_bobby",
        "joyal_antony"
    ]

    # Check if username is authorized
    if user_data.username not in AUTHORIZED_USERS:
        raise HTTPException(
            status_code=403,
            detail=f"Access denied. Only authorized team members can register. Use one of: {', '.join(AUTHORIZED_USERS)}"
        )

    query = select(users).where(users.c.username == user_data.username)
    existing_user = await database.fetch_one(query)

    if existing_user:
        # If user exists, return their info (allow re-registration for team members)
        return {
            "success": True,
            "message": "User already registered. You can proceed to biometric setup.",
            "user_id": existing_user.id,
            "username": existing_user.username,
            "has_face": bool(existing_user.face_encoding),
            "has_voice": bool(existing_user.voice_print)
        }

    query = insert(users).values(
        username=user_data.username,
        email=user_data.email,
        is_active=True,
        created_at=datetime.utcnow(),
        updated_at=datetime.utcnow()
    )
    user_id = await database.execute(query)

    return {
        "success": True,
        "message": "Team member registered successfully. Please complete biometric enrollment.",
        "user_id": user_id,
        "username": user_data.username,
        "has_face": False,
        "has_voice": False
    }

@router.post("/login")
async def login_user(user_data: UserLogin):
    """Log in a user if they exist and are active"""

    # Define authorized team members
    AUTHORIZED_USERS = [
        "fenny_mary",
        "george_bobby",
        "joyal_antony"
    ]

    # Check if username is authorized
    if user_data.username not in AUTHORIZED_USERS:
        raise HTTPException(
            status_code=403,
            detail=f"Access denied. Only authorized team members can login. Use one of: {', '.join(AUTHORIZED_USERS)}"
        )

    query = select(users).where(users.c.username == user_data.username)
    user = await database.fetch_one(query)

    if not user:
        raise HTTPException(
            status_code=404,
            detail=f"Team member '{user_data.username}' not found in database. Please run the initialization script first."
        )

    if not user["is_active"]:
        raise HTTPException(status_code=400, detail="User account is inactive")

    # Determine user's current status
    has_face = bool(user["face_encoding"])
    has_voice = bool(user["voice_print"])

    if not has_face and not has_voice:
        message = "Login successful. Please complete face and voice enrollment."
    elif not has_face:
        message = "Login successful. Please complete face enrollment."
    elif not has_voice:
        message = "Login successful. Please complete voice enrollment."
    else:
        message = "Login successful. Ready for biometric verification."

    return {
        "success": True,
        "message": message,
        "user_id": user["id"],
        "username": user["username"],
        "has_face": has_face,
        "has_voice": has_voice,
        "enrollment_status": {
            "face_enrolled": has_face,
            "voice_enrolled": has_voice,
            "ready_for_auth": has_face and has_voice
        }
    }

@router.get("/user/{username}")
async def get_user(username: str):
    """Fetch user details by username"""
    query = select(users).where(users.c.username == username)
    user = await database.fetch_one(query)

    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    return {
        "user_id": user["id"],
        "username": user["username"],
        "email": user["email"],
        "is_active": user["is_active"],
        "has_face": bool(user["face_encoding"]),
        "has_voice": bool(user["voice_print"]),
        "created_at": user["created_at"]
    }

@router.get("/authorized-users")
async def get_authorized_users():
    """Get list of authorized team members and their enrollment status"""
    AUTHORIZED_USERS = [
        "fenny_mary",
        "george_bobby",
        "joyal_antony"
    ]

    user_status = []
    try:
        for username in AUTHORIZED_USERS:
            query = select(users).where(users.c.username == username)
            user = await database.fetch_one(query)
            print(f"DEBUG: Querying user {username}, result: {user}")

            if user:
                user_status.append({
                    "username": username,
                    "registered": True,
                    "has_face": bool(user["face_encoding"]),
                    "has_voice": bool(user["voice_print"]),
                    "ready_for_auth": bool(user["face_encoding"]) and bool(user["voice_print"]),
                    "created_at": user["created_at"]
                })
            else:
                user_status.append({
                    "username": username,
                    "registered": False,
                    "has_face": False,
                    "has_voice": False,
                    "ready_for_auth": False,
                    "created_at": None
                })
    except Exception as e:
        print(f"DEBUG: Database error: {e}")
        # Return default status if database error
        for username in AUTHORIZED_USERS:
            user_status.append({
                "username": username,
                "registered": False,
                "has_face": False,
                "has_voice": False,
                "ready_for_auth": False,
                "created_at": None
            })

    return {
        "success": True,
        "authorized_users": user_status,
        "total_users": len(AUTHORIZED_USERS),
        "registered_users": len([u for u in user_status if u["registered"]]),
        "ready_users": len([u for u in user_status if u["ready_for_auth"]])
    }

@router.get("/debug-db")
async def debug_database():
    """Debug endpoint to test database connection"""
    from app.database import DATABASE_URL, DB_PATH
    import os
    import sqlite3

    try:
        # Test direct SQLite connection (use absolute path)
        db_file = os.path.join(os.getcwd(), "biometric_auth.db")
        direct_conn = sqlite3.connect(db_file)
        direct_cursor = direct_conn.cursor()
        direct_cursor.execute("SELECT COUNT(*) FROM users")
        direct_count = direct_cursor.fetchone()[0]
        direct_conn.close()

        # Test FastAPI database connection
        query = select(users)
        result = await database.fetch_all(query)

        return {
            "success": True,
            "database_connected": True,
            "database_url": DATABASE_URL,
            "database_path": DB_PATH,
            "database_exists": os.path.exists(DB_PATH),
            "direct_sqlite_users": direct_count,
            "fastapi_users_found": len(result),
            "users": [{"username": u["username"], "email": u["email"]} for u in result]
        }
    except Exception as e:
        return {
            "success": False,
            "database_connected": False,
            "database_url": DATABASE_URL,
            "error": str(e)
        }

# -------------------------
# Authentication Logging
# -------------------------

async def log_authentication(user_id, username, auth_type, success, confidence_score, ip_address, user_agent):
    """Log each authentication attempt (face/audio)"""
    query = insert(authentication_logs).values(
        user_id=user_id,
        username=username,
        auth_type=auth_type,
        success=success,
        confidence_score=str(round(confidence_score, 2)),
        ip_address=ip_address,
        user_agent=user_agent,
        timestamp=datetime.utcnow()
    )
    await database.execute(query)
