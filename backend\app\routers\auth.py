from app.database import database
from app.models import authentication_logs
from sqlalchemy import insert

async def log_authentication(user_id, username, auth_type, success, confidence_score, ip_address, user_agent):
    query = insert(authentication_logs).values(
        user_id=user_id,
        username=username,
        auth_type=auth_type,
        success=success,
        confidence_score=str(round(confidence_score, 2)),
        ip_address=ip_address,
        user_agent=user_agent
    )
    await database.execute(query)