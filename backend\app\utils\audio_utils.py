import numpy as np
import librosa
from scipy.spatial.distance import cosine

def extract_mfcc_features(file) -> np.ndarray:
    try:
        y, sr = librosa.load(file, sr=16000)
        mfcc = librosa.feature.mfcc(y=y, sr=sr, n_mfcc=13)
        mfcc_mean = np.mean(mfcc, axis=1)
        return mfcc_mean
    except Exception as e:
        print("MFCC extraction failed:", e)
        return None

def cosine_similarity(vec1, vec2):
    try:
        return 1 - cosine(vec1, vec2)
    except:
        return 0.0