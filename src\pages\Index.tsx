
import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import LoginForm from '../components/LoginForm';
import BiometricAuth from '../components/BiometricAuth';
import Dashboard from '../components/Dashboard';

type AuthStep = 'login' | 'biometric' | 'authenticated';

const Index = () => {
  const [currentStep, setCurrentStep] = useState<AuthStep>('login');
  const [userName, setUserName] = useState('');

  const handleNameSubmit = (name: string) => {
    setUserName(name);
    setCurrentStep('biometric');
  };

  const handleBiometricSuccess = () => {
    setCurrentStep('authenticated');
  };

  const handleLogout = () => {
    setUserName('');
    setCurrentStep('login');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden">
      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-32 w-80 h-80 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse"></div>
        <div className="absolute -bottom-40 -left-32 w-80 h-80 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-60 h-60 bg-indigo-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse delay-500"></div>
      </div>

      <AnimatePresence mode="wait">
        {currentStep === 'login' && (
          <motion.div
            key="login"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.5 }}
          >
            <LoginForm onSubmit={handleNameSubmit} />
          </motion.div>
        )}

        {currentStep === 'biometric' && (
          <motion.div
            key="biometric"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 1.1 }}
            transition={{ duration: 0.5 }}
          >
            <BiometricAuth 
              userName={userName} 
              onSuccess={handleBiometricSuccess}
              onBack={() => setCurrentStep('login')}
            />
          </motion.div>
        )}

        {currentStep === 'authenticated' && (
          <motion.div
            key="dashboard"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            transition={{ duration: 0.5 }}
          >
            <Dashboard userName={userName} onLogout={handleLogout} />
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default Index;
