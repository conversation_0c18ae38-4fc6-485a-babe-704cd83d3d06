from fastapi import APIRouter, HTTPException, UploadFile, File, Form
from pydantic import BaseModel
import os
from app.utils.deepface_utils import verify_face, enroll_face, get_face_confidence
from app.database import database
from app.models import users
from sqlalchemy import select, update

router = APIRouter()

class FaceInput(BaseModel):
    base64_image: str
    username: str

@router.post("/enroll")
async def enroll_face_route(username: str = Form(...), image: UploadFile = File(...)):
    """Enroll a user's face for recognition"""
    try:
        # Check if user exists
        query = select(users).where(users.c.username == username)
        user = await database.fetch_one(query)

        if not user:
            raise HTTPException(status_code=404, detail="User not found")

        # Read image data
        image_data = await image.read()

        # Convert to base64
        import base64
        base64_image = base64.b64encode(image_data).decode('utf-8')

        # Enroll the face
        success = enroll_face(base64_image, username)

        if success:
            # Update user record to indicate face is enrolled
            query = update(users).where(users.c.id == user.id).values(face_encoding="enrolled")
            await database.execute(query)

            return {
                "success": True,
                "message": "Face enrolled successfully",
                "user_id": user.id
            }
        else:
            raise HTTPException(status_code=400, detail="Failed to enroll face")

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Enrollment failed: {str(e)}")

@router.post("/verify-face")
async def verify_face_route(data: FaceInput):
    """Verify a user's face"""
    try:
        image_path = f"registered_faces/{data.username}.jpg"

        if not os.path.exists(image_path):
            return {
                "success": False,
                "message": "No enrolled face found for this user",
                "confidence": 0.0
            }

        # Get confidence score
        confidence = get_face_confidence(data.base64_image, image_path)

        # Verify face
        is_verified = verify_face(data.base64_image, image_path)

        return {
            "success": is_verified,
            "message": "Face verified successfully" if is_verified else "Face verification failed",
            "confidence": confidence
        }

    except Exception as e:
        return {
            "success": False,
            "message": f"Verification error: {str(e)}",
            "confidence": 0.0
        }
