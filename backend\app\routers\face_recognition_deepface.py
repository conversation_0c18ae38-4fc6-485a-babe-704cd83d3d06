from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
import os

from app.utils.deepface_utils import verify_face

router = APIRouter()

class FaceInput(BaseModel):
    base64_image: str
    username: str

@router.post("/verify-face")
async def verify_face_route(data: FaceInput):
    image_path = f"registered_faces/{data.username}.jpg"  # make sure you store registered images
    if not os.path.exists(image_path):
        raise HTTPException(status_code=404, detail="User image not found.")

    if verify_face(data.base64_image, image_path):
        return {"status": "success", "message": "Face Verified"}
    else:
        return {"status": "failed", "message": "Face Not Recognized"}
