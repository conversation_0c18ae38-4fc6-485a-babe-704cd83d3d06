#!/usr/bin/env python3
"""
Quick Start Script for Persona Audio Gatekeeper
Starts both backend and frontend servers
"""

import os
import sys
import subprocess
import time
import threading
import signal

def start_backend():
    """Start the backend server"""
    try:
        os.chdir("backend")
        
        # Use the virtual environment Python
        if os.name == 'nt':  # Windows
            python_path = "venv\\Scripts\\python.exe"
        else:  # Unix/Linux/Mac
            python_path = "venv/bin/python"
        
        print("🚀 Starting backend server...")
        print("📍 Backend URL: http://127.0.0.1:8000")
        print("📖 API Docs: http://127.0.0.1:8000/docs")
        
        # Start the backend
        process = subprocess.Popen([python_path, "run.py"])
        return process
        
    except Exception as e:
        print(f"❌ Failed to start backend: {e}")
        return None

def start_frontend():
    """Start the frontend server"""
    try:
        # Wait a bit for backend to start
        time.sleep(3)
        
        print("\n🎨 Starting frontend server...")
        print("📍 Frontend URL: http://localhost:5173")
        
        # Start the frontend
        process = subprocess.Popen(["npm", "run", "dev"])
        return process
        
    except Exception as e:
        print(f"❌ Failed to start frontend: {e}")
        return None

def signal_handler(sig, frame):
    """Handle Ctrl+C gracefully"""
    print("\n\n🛑 Shutting down servers...")
    sys.exit(0)

def main():
    """Main function to start both servers"""
    print("🚀 Starting Persona Audio Gatekeeper System")
    print("=" * 50)
    
    # Set up signal handler for graceful shutdown
    signal.signal(signal.SIGINT, signal_handler)
    
    # Start backend in a separate thread
    backend_process = start_backend()
    if not backend_process:
        print("❌ Failed to start backend. Exiting.")
        return
    
    # Start frontend
    frontend_process = start_frontend()
    if not frontend_process:
        print("❌ Failed to start frontend. Backend is still running.")
        return
    
    print("\n✅ Both servers are starting up...")
    print("\n📋 Team Member Instructions:")
    print("1. Open http://localhost:5173 in your browser")
    print("2. Click 'Show authorized team members'")
    print("3. Select your username:")
    print("   • team_member_1")
    print("   • team_member_2") 
    print("   • team_member_3")
    print("4. Complete biometric enrollment")
    print("5. Test authentication")
    
    print("\n🔧 System URLs:")
    print("• Frontend: http://localhost:5173")
    print("• Backend API: http://127.0.0.1:8000")
    print("• API Documentation: http://127.0.0.1:8000/docs")
    
    print("\n⌨️  Press Ctrl+C to stop both servers")
    
    try:
        # Wait for processes to complete
        backend_process.wait()
        frontend_process.wait()
    except KeyboardInterrupt:
        print("\n🛑 Stopping servers...")
        backend_process.terminate()
        frontend_process.terminate()

if __name__ == "__main__":
    main()
