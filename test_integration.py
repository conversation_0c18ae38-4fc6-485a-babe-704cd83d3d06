#!/usr/bin/env python3
"""
Integration test script for Persona Audio Gatekeeper
Tests all API endpoints to ensure backend is working correctly
"""

import requests
import json
import base64
import io
from PIL import Image
import numpy as np

# Configuration
BASE_URL = "http://127.0.0.1:8000"
TEST_USERNAME = "testuser"
TEST_EMAIL = "<EMAIL>"

def create_test_image():
    """Create a simple test image"""
    # Create a 200x200 RGB image with a simple pattern
    img = Image.new('RGB', (200, 200), color='white')
    pixels = img.load()
    
    # Draw a simple face-like pattern
    for i in range(200):
        for j in range(200):
            # Simple circular pattern
            center_x, center_y = 100, 100
            distance = ((i - center_x) ** 2 + (j - center_y) ** 2) ** 0.5
            if distance < 80:
                pixels[i, j] = (100, 150, 200)  # Light blue
            elif distance < 90:
                pixels[i, j] = (50, 100, 150)   # Darker blue
    
    # Convert to bytes
    img_byte_arr = io.BytesIO()
    img.save(img_byte_arr, format='JPEG')
    img_byte_arr = img_byte_arr.getvalue()
    
    return img_byte_arr

def create_test_audio():
    """Create a simple test audio file (placeholder)"""
    # This is a placeholder - in a real test you'd create actual audio data
    # For now, we'll just create some dummy bytes
    return b"dummy_audio_data_for_testing"

def test_health_check():
    """Test the health check endpoint"""
    print("🔍 Testing health check...")
    try:
        response = requests.get(f"{BASE_URL}/health")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Health check passed: {data}")
            return True
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return False

def test_user_registration():
    """Test user registration"""
    print(f"🔍 Testing user registration for {TEST_USERNAME}...")
    try:
        data = {
            "username": TEST_USERNAME,
            "email": TEST_EMAIL
        }
        response = requests.post(f"{BASE_URL}/api/auth/register", json=data)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Registration successful: {result}")
            return True
        elif response.status_code == 400 and "already registered" in response.json().get("detail", ""):
            print(f"ℹ️  User already exists, proceeding...")
            return True
        else:
            print(f"❌ Registration failed: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ Registration error: {e}")
        return False

def test_user_login():
    """Test user login"""
    print(f"🔍 Testing user login for {TEST_USERNAME}...")
    try:
        data = {"username": TEST_USERNAME}
        response = requests.post(f"{BASE_URL}/api/auth/login", json=data)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Login successful: {result}")
            return True
        else:
            print(f"❌ Login failed: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ Login error: {e}")
        return False

def test_face_enrollment():
    """Test face enrollment"""
    print(f"🔍 Testing face enrollment for {TEST_USERNAME}...")
    try:
        # Create test image
        image_data = create_test_image()
        
        # Prepare form data
        files = {
            'image': ('test_face.jpg', image_data, 'image/jpeg')
        }
        data = {
            'username': TEST_USERNAME
        }
        
        response = requests.post(f"{BASE_URL}/api/face/enroll", files=files, data=data)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Face enrollment successful: {result}")
            return True
        else:
            print(f"❌ Face enrollment failed: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ Face enrollment error: {e}")
        return False

def test_face_verification():
    """Test face verification"""
    print(f"🔍 Testing face verification for {TEST_USERNAME}...")
    try:
        # Create test image and convert to base64
        image_data = create_test_image()
        base64_image = base64.b64encode(image_data).decode('utf-8')
        
        data = {
            "base64_image": base64_image,
            "username": TEST_USERNAME
        }
        
        response = requests.post(f"{BASE_URL}/api/face/verify-face", json=data)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Face verification completed: {result}")
            return True
        else:
            print(f"❌ Face verification failed: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ Face verification error: {e}")
        return False

def test_voice_enrollment():
    """Test voice enrollment"""
    print(f"🔍 Testing voice enrollment for {TEST_USERNAME}...")
    try:
        # Create test audio
        audio_data = create_test_audio()
        
        # Prepare form data
        files = {
            'audio': ('test_voice.wav', audio_data, 'audio/wav')
        }
        data = {
            'username': TEST_USERNAME
        }
        
        response = requests.post(f"{BASE_URL}/api/biometric/enroll-voice", files=files, data=data)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Voice enrollment successful: {result}")
            return True
        else:
            print(f"❌ Voice enrollment failed: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ Voice enrollment error: {e}")
        return False

def test_voice_verification():
    """Test voice verification"""
    print(f"🔍 Testing voice verification for {TEST_USERNAME}...")
    try:
        # Create test audio
        audio_data = create_test_audio()
        
        # Prepare form data
        files = {
            'audio': ('test_voice.wav', audio_data, 'audio/wav')
        }
        data = {
            'username': TEST_USERNAME
        }
        
        response = requests.post(f"{BASE_URL}/api/biometric/verify-voice", files=files, data=data)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Voice verification completed: {result}")
            return True
        else:
            print(f"❌ Voice verification failed: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ Voice verification error: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Starting Persona Audio Gatekeeper Integration Tests")
    print("=" * 60)
    
    tests = [
        ("Health Check", test_health_check),
        ("User Registration", test_user_registration),
        ("User Login", test_user_login),
        ("Face Enrollment", test_face_enrollment),
        ("Face Verification", test_face_verification),
        ("Voice Enrollment", test_voice_enrollment),
        ("Voice Verification", test_voice_verification),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 Running: {test_name}")
        if test_func():
            passed += 1
        print("-" * 40)
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Your backend is ready for frontend integration.")
    else:
        print("⚠️  Some tests failed. Please check the backend setup.")
        
    print("\n🔗 Next steps:")
    print("1. Start your React frontend: npm run dev")
    print("2. Test the full integration in the browser")
    print("3. Register a new user and test biometric authentication")

if __name__ == "__main__":
    main()
