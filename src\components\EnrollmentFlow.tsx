import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Camera, Mic, CheckCircle, XCircle, Loader2, AlertCircle, ArrowLeft, ArrowRight } from 'lucide-react';
import { useMediaCapture } from '@/hooks/useMediaCapture';
import { apiService } from '@/services/api';

interface EnrollmentFlowProps {
  userName: string;
  onComplete: () => void;
  onBack: () => void;
}

type EnrollmentStep = 'intro' | 'face' | 'voice' | 'complete';

interface EnrollmentResults {
  faceEnrolled?: boolean;
  voiceEnrolled?: boolean;
  faceMessage?: string;
  voiceMessage?: string;
}

// Voice enrollment phrase for consistency
const VOICE_ENROLLMENT_PHRASE = "Hello, this is my voice for secure authentication. My name is";
const VOICE_RECORDING_DURATION = 5000; // 5 seconds

const EnrollmentFlow = ({ userName, onComplete, onBack }: EnrollmentFlowProps) => {
  const [currentStep, setCurrentStep] = useState<EnrollmentStep>('intro');
  const [isLoading, setIsLoading] = useState(false);
  const [results, setResults] = useState<EnrollmentResults>({});
  const [error, setError] = useState<string | null>(null);
  const [videoStartAttempts, setVideoStartAttempts] = useState(0);

  const {
    isVideoReady,
    isRecording,
    error: mediaError,
    hasPermissions,
    videoRef,
    startVideo,
    stopVideo,
    captureImage,
    startRecording,
    stopRecording,
    requestPermissions,
  } = useMediaCapture();

  // Effect to start video when face step is rendered and video element is available
  useEffect(() => {
    if (currentStep === 'face' && !isVideoReady && !isLoading) {
      // Use a longer delay to ensure the video element is fully rendered
      const timer = setTimeout(() => {
        console.log('🔍 Checking if video element is available...');
        console.log('Video ref current:', videoRef.current);

        if (videoRef.current) {
          console.log('✅ Video element found, starting video...');
          startVideo().catch(error => {
            console.error('❌ Video start failed:', error);
            setError(`Camera failed to start: ${error.message}`);
          });
        } else {
          console.error('❌ Video element not available after delay');
          setError('Video element not available. Please try the manual start button.');
        }
      }, 1000); // Increased delay to 1 second

      return () => clearTimeout(timer);
    }
  }, [currentStep, isVideoReady, isLoading, startVideo, videoRef]);

  // Initialize permissions on component mount
  useEffect(() => {
    console.log('🎬 EnrollmentFlow component mounted');
    requestPermissions();
  }, [requestPermissions]);

  // Debug effect to monitor video ref changes
  useEffect(() => {
    console.log('📹 Video ref changed:', videoRef.current ? 'Available' : 'Not available');
  }, [videoRef.current]);

  const startEnrollment = async () => {
    setError(null);
    setIsLoading(true);

    try {
      console.log('🎥 Starting enrollment - requesting permissions...');

      if (!hasPermissions) {
        console.log('🔐 Requesting camera/microphone permissions...');
        const granted = await requestPermissions();
        if (!granted) {
          setError('Camera and microphone permissions are required for enrollment. Please allow access and try again.');
          setIsLoading(false);
          return;
        }
        console.log('✅ Permissions granted');
      }

      console.log('📹 Moving to face step...');
      setCurrentStep('face');
      setVideoStartAttempts(0); // Reset retry counter
      setIsLoading(false); // Let the useEffect handle video start

    } catch (error) {
      console.error('❌ Error starting enrollment:', error);
      setError(`Failed to start enrollment: ${error instanceof Error ? error.message : 'Unknown error'}`);
      setIsLoading(false);
    }
  };

  const handleFaceEnrollment = async () => {
    if (!isVideoReady) {
      setError('Camera not ready. Please ensure camera permissions are granted and try again.');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      console.log('📸 Capturing face image...');
      const imageBlob = await captureImage();
      if (!imageBlob) {
        throw new Error('Failed to capture face image from camera');
      }

      console.log('📤 Uploading face data for enrollment...');
      // Call face enrollment API
      const result = await apiService.enrollFace(userName, imageBlob);

      console.log('📥 Face enrollment result:', result);

      setResults(prev => ({
        ...prev,
        faceEnrolled: result.success,
        faceMessage: result.message
      }));

      if (result.success) {
        console.log('✅ Face enrollment successful, moving to voice enrollment');
        // Move to voice enrollment
        stopVideo();
        setCurrentStep('voice');
      } else {
        console.error('❌ Face enrollment failed:', result.message);
        setError(result.message || 'Face enrollment failed');
      }
    } catch (error) {
      console.error('❌ Face enrollment error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Face enrollment failed';
      setError(errorMessage);
      setResults(prev => ({
        ...prev,
        faceEnrolled: false,
        faceMessage: errorMessage
      }));
    } finally {
      setIsLoading(false);
    }
  };

  const handleVoiceEnrollment = async () => {
    setIsLoading(true);
    setError(null);

    try {
      console.log('🎤 Starting voice recording...');
      await startRecording();

      // Record for 5 seconds
      setTimeout(async () => {
        try {
          console.log('🛑 Stopping voice recording...');
          const audioBlob = await stopRecording();
          if (!audioBlob) {
            throw new Error('Failed to record voice');
          }

          console.log('📤 Uploading voice data for enrollment...');
          // Call voice enrollment API
          const result = await apiService.enrollVoice(userName, audioBlob);

          console.log('📥 Voice enrollment result:', result);

          setResults(prev => ({
            ...prev,
            voiceEnrolled: result.success,
            voiceMessage: result.message
          }));

          if (result.success) {
            setCurrentStep('complete');
          } else {
            setError(result.message || 'Voice enrollment failed');
          }

        } catch (recordingError) {
          console.error('❌ Voice recording error:', recordingError);
          const errorMessage = recordingError instanceof Error ? recordingError.message : 'Voice enrollment failed';
          setError(errorMessage);
          setResults(prev => ({
            ...prev,
            voiceEnrolled: false,
            voiceMessage: errorMessage
          }));
        } finally {
          setIsLoading(false);
        }
      }, VOICE_RECORDING_DURATION);
    } catch (error) {
      console.error('❌ Voice enrollment error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Voice enrollment failed';
      setError(errorMessage);
      setResults(prev => ({
        ...prev,
        voiceEnrolled: false,
        voiceMessage: errorMessage
      }));
      setIsLoading(false);
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 'intro':
        return (
          <div className="text-center space-y-6">
            <div className="space-y-4">
              <h3 className="text-xl font-semibold text-white">Welcome to Biometric Enrollment</h3>
              <p className="text-gray-300">
                Hi <span className="text-blue-400 font-medium">{userName}</span>! 
                We need to register your face and voice for secure authentication.
              </p>
            </div>
            
            <div className="bg-white/5 rounded-lg p-4 space-y-3">
              <h4 className="text-white font-medium">Enrollment Process:</h4>
              <div className="space-y-2 text-sm text-gray-300">
                <div className="flex items-center gap-2">
                  <Camera className="w-4 h-4 text-blue-400" />
                  <span>Step 1: Face Registration - Look directly at the camera</span>
                </div>
                <div className="flex items-center gap-2">
                  <Mic className="w-4 h-4 text-green-400" />
                  <span>Step 2: Voice Registration - Speak clearly for 3 seconds</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-purple-400" />
                  <span>Step 3: Complete - Ready for authentication</span>
                </div>
              </div>
            </div>

            <Button
              onClick={startEnrollment}
              disabled={!hasPermissions || isLoading}
              className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
            >
              {isLoading ? (
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              ) : (
                <ArrowRight className="w-4 h-4 mr-2" />
              )}
              Start Enrollment
            </Button>
          </div>
        );

      case 'face':
        return (
          <div className="space-y-6">
            <div className="text-center">
              <h3 className="text-xl font-semibold text-white mb-2">Face Registration</h3>
              <p className="text-gray-300">Position your face in the center and click capture</p>
            </div>

            {/* Camera Display */}
            <div className="relative bg-black rounded-lg overflow-hidden aspect-video">
              {/* Always render the video element */}
              <video
                ref={videoRef}
                autoPlay
                muted
                playsInline
                className={`w-full h-full object-cover ${isVideoReady ? 'block' : 'hidden'}`}
                onLoadedMetadata={() => {
                  console.log('📹 Video metadata loaded from element');
                }}
                onCanPlay={() => {
                  console.log('📹 Video can play from element');
                }}
                onError={(e) => {
                  console.error('📹 Video element error:', e);
                }}
              />

              {isVideoReady && (
                <>
                  {/* Face capture overlay */}
                  <div className="absolute inset-0 flex items-center justify-center">
                    <motion.div
                      animate={{
                        scale: [1, 1.05, 1],
                        borderColor: ['#3B82F6', '#10B981', '#3B82F6']
                      }}
                      transition={{
                        scale: { repeat: Infinity, duration: 2 },
                        borderColor: { repeat: Infinity, duration: 1.5 }
                      }}
                      className="w-48 h-48 border-4 rounded-full"
                    />
                  </div>
                  {/* Camera status indicator */}
                  <div className="absolute top-2 right-2 bg-green-500 text-white px-2 py-1 rounded text-xs">
                    📹 Camera Active
                  </div>
                </>
              )}

              {!isVideoReady && (
                <div className="absolute inset-0 w-full h-full flex items-center justify-center">
                  <div className="text-center space-y-4">
                    <Camera className="w-16 h-16 text-gray-400 mx-auto" />
                    <div>
                      <p className="text-gray-400 mb-2">
                        {mediaError ? 'Camera Error' : 'Starting camera...'}
                      </p>
                      {mediaError && (
                        <p className="text-red-400 text-sm mb-3">{mediaError}</p>
                      )}
                      {!isLoading && (
                        <div className="space-y-2">
                          <div className="flex flex-col gap-2">
                            <Button
                              onClick={async () => {
                                console.log('🔄 Manual camera start requested');
                                console.log('🔍 Video element check:', videoRef.current);
                                setError(null);
                                try {
                                  if (!videoRef.current) {
                                    throw new Error('Video element not found. Please refresh the page.');
                                  }
                                  await startVideo();
                                } catch (error) {
                                  console.error('❌ Manual camera start failed:', error);
                                  setError(`Camera start failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
                                }
                              }}
                              variant="outline"
                              size="sm"
                              className="text-white border-white/20 hover:bg-white/10"
                            >
                              <Camera className="w-4 h-4 mr-2" />
                              Start Camera
                            </Button>

                            {!videoRef.current && (
                              <Button
                                onClick={() => {
                                  console.log('🔄 Force refresh requested');
                                  window.location.reload();
                                }}
                                variant="outline"
                                size="sm"
                                className="text-yellow-400 border-yellow-400/20 hover:bg-yellow-400/10"
                              >
                                🔄 Refresh Page
                              </Button>
                            )}
                          </div>

                          <div className="text-xs text-gray-500 space-y-1">
                            <p>Video element: {videoRef.current ? '✅ Available' : '❌ Not found'}</p>
                            <p>Permissions: {hasPermissions ? '✅ Granted' : '❌ Denied'}</p>
                            <p>Camera ready: {isVideoReady ? '✅ Ready' : '❌ Not ready'}</p>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )}
            </div>

            <Button
              onClick={handleFaceEnrollment}
              disabled={!isVideoReady || isLoading}
              className="w-full bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800"
            >
              {isLoading ? (
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              ) : (
                <Camera className="w-4 h-4 mr-2" />
              )}
              Capture Face
            </Button>
          </div>
        );

      case 'voice':
        return (
          <div className="space-y-6">
            <div className="text-center">
              <h3 className="text-xl font-semibold text-white mb-2">Voice Registration</h3>
              <p className="text-gray-300">
                Click the button and read the phrase below clearly
              </p>
            </div>

            {/* Voice Phrase to Read */}
            <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4">
              <h4 className="text-blue-400 font-medium mb-2">📖 Please read this phrase:</h4>
              <div className="bg-white/5 rounded p-3 border border-white/10">
                <p className="text-white text-lg font-medium text-center leading-relaxed">
                  "{VOICE_ENROLLMENT_PHRASE} <span className="text-blue-400">{userName}</span>"
                </p>
              </div>
              <p className="text-sm text-gray-400 mt-2 text-center">
                Speak clearly and naturally. Recording will last {VOICE_RECORDING_DURATION / 1000} seconds.
              </p>
            </div>

            {/* Voice Recording Display */}
            <div className="bg-black rounded-lg aspect-video flex items-center justify-center">
              <div className="text-center">
                <motion.div
                  animate={isRecording ? {
                    scale: [1, 1.2, 1],
                    opacity: [0.5, 1, 0.5]
                  } : {}}
                  transition={{
                    repeat: isRecording ? Infinity : 0,
                    duration: 1
                  }}
                >
                  <Mic className={`w-16 h-16 mx-auto mb-4 ${
                    isRecording ? 'text-red-400' : 'text-blue-400'
                  }`} />
                </motion.div>
                <p className={`text-lg font-medium ${
                  isRecording ? 'text-red-400' : 'text-white'
                }`}>
                  {isRecording ? '🔴 Recording... Read the phrase above!' : 'Ready to record'}
                </p>
                {isRecording && (
                  <p className="text-sm text-gray-400 mt-2">
                    Recording will stop automatically in {VOICE_RECORDING_DURATION / 1000} seconds
                  </p>
                )}
                {!isRecording && (
                  <p className="text-sm text-blue-400 mt-2">
                    Make sure you're in a quiet environment
                  </p>
                )}
              </div>
            </div>

            <Button
              onClick={handleVoiceEnrollment}
              disabled={isLoading || isRecording}
              className="w-full bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800"
            >
              {isLoading ? (
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              ) : isRecording ? (
                <Mic className="w-4 h-4 mr-2" />
              ) : (
                <Mic className="w-4 h-4 mr-2" />
              )}
              {isRecording ? 'Recording...' : 'Start Voice Recording'}
            </Button>
          </div>
        );

      case 'complete':
        return (
          <div className="text-center space-y-6">
            <div className="space-y-4">
              <CheckCircle className="w-16 h-16 text-green-400 mx-auto" />
              <h3 className="text-xl font-semibold text-white">Enrollment Complete!</h3>
              <p className="text-gray-300">
                Your biometric data has been successfully registered.
              </p>
            </div>

            {/* Results Summary */}
            <div className="bg-white/5 rounded-lg p-4 space-y-3">
              <h4 className="text-white font-medium">Enrollment Results:</h4>
              <div className="space-y-2 text-sm">
                <div className="flex items-center justify-between">
                  <span className="text-gray-300">Face Registration:</span>
                  <div className="flex items-center gap-2">
                    {results.faceEnrolled ? (
                      <CheckCircle className="w-4 h-4 text-green-400" />
                    ) : (
                      <XCircle className="w-4 h-4 text-red-400" />
                    )}
                    <span className={results.faceEnrolled ? 'text-green-400' : 'text-red-400'}>
                      {results.faceEnrolled ? 'Success' : 'Failed'}
                    </span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-300">Voice Registration:</span>
                  <div className="flex items-center gap-2">
                    {results.voiceEnrolled ? (
                      <CheckCircle className="w-4 h-4 text-green-400" />
                    ) : (
                      <XCircle className="w-4 h-4 text-red-400" />
                    )}
                    <span className={results.voiceEnrolled ? 'text-green-400' : 'text-red-400'}>
                      {results.voiceEnrolled ? 'Success' : 'Failed'}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <Button
              onClick={onComplete}
              className="w-full bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800"
            >
              <CheckCircle className="w-4 h-4 mr-2" />
              Continue to Authentication
            </Button>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
        className="w-full max-w-2xl"
      >
        <Card className="bg-white/10 backdrop-blur-lg border-white/20 shadow-2xl">
          <CardHeader className="text-center relative">
            <Button
              variant="ghost"
              onClick={onBack}
              className="absolute top-4 left-4 text-white hover:bg-white/10"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back
            </Button>
            <CardTitle className="text-2xl font-bold text-white">
              Biometric Enrollment
            </CardTitle>
            <CardDescription className="text-gray-300">
              Step {currentStep === 'intro' ? '0' : currentStep === 'face' ? '1' : currentStep === 'voice' ? '2' : '3'} of 3
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Debug Info (only in development) */}
            {process.env.NODE_ENV === 'development' && (
              <div className="text-xs text-gray-500 bg-gray-900/20 p-2 rounded">
                <div>Permissions: {hasPermissions ? '✅' : '❌'}</div>
                <div>Video Ready: {isVideoReady ? '✅' : '❌'}</div>
                <div>Video Element: {videoRef.current ? '✅' : '❌'}</div>
                <div>Recording: {isRecording ? '🔴' : '⚫'}</div>
                <div>Step: {currentStep}</div>
                <div>Start Attempts: {videoStartAttempts}</div>
              </div>
            )}

            {/* Error Display */}
            {(error || mediaError) && (
              <motion.div
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                className="p-3 bg-red-500/10 border border-red-500/20 rounded-lg"
              >
                <div className="flex items-center gap-2 text-red-400">
                  <AlertCircle className="w-4 h-4" />
                  <span className="text-sm">{error || mediaError}</span>
                </div>
              </motion.div>
            )}

            {renderStepContent()}
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
};

export default EnrollmentFlow;
