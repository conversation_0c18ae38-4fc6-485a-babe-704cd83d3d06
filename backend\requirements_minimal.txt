# Minimal requirements without problematic packages
# Use this if face_recognition installation fails

# Core FastAPI dependencies
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6

# Basic computer vision (without face_recognition)
numpy==1.24.3
pillow==10.0.1
opencv-python==********

# Audio processing (without pyaudio if problematic)
speechrecognition==3.10.0
pydub==0.25.1

# Security and Authentication
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4

# Database
sqlalchemy==2.0.23
databases[sqlite]==0.8.0
aiosqlite==0.19.0

# Configuration
python-dotenv==1.0.0

# WebSocket support
websockets==12.0

# Additional utilities
requests==2.31.0
