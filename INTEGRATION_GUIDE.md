# Frontend-Backend Integration Guide

This guide explains how to integrate the Python backend with your existing React frontend for face and audio recognition.

## Backend Setup

### 1. Install and Start Backend

```bash
cd backend

# Automatic setup (recommended)
python setup.py

# Start the server
python run.py
```

The backend will be available at `http://localhost:8000`

### 2. Test Backend

```bash
# Test the API
python test_api.py

# Check API documentation
# Visit: http://localhost:8000/docs
```

## Frontend Integration

### 1. Update BiometricAuth Component

Replace the mock authentication in `src/components/BiometricAuth.tsx` with real API calls:

```typescript
// Add these API functions at the top of the file
const API_BASE_URL = 'http://localhost:8000/api';

const registerUser = async (username: string) => {
  const response = await fetch(`${API_BASE_URL}/auth/register`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ username, email: `${username}@example.com` })
  });
  return response.json();
};

const enrollFace = async (username: string, imageBlob: Blob) => {
  const formData = new FormData();
  formData.append('username', username);
  formData.append('image', imageBlob, 'face.jpg');
  
  const response = await fetch(`${API_BASE_URL}/face/enroll`, {
    method: 'POST',
    body: formData
  });
  return response.json();
};

const enrollVoice = async (username: string, audioBlob: Blob) => {
  const formData = new FormData();
  formData.append('username', username);
  formData.append('audio', audioBlob, 'voice.wav');
  
  const response = await fetch(`${API_BASE_URL}/audio/enroll`, {
    method: 'POST',
    body: formData
  });
  return response.json();
};

const authenticateBiometric = async (username: string, imageBlob: Blob, audioBlob: Blob) => {
  const formData = new FormData();
  formData.append('username', username);
  formData.append('face_image', imageBlob, 'face.jpg');
  formData.append('voice_audio', audioBlob, 'voice.wav');
  
  const response = await fetch(`${API_BASE_URL}/biometric/authenticate`, {
    method: 'POST',
    body: formData
  });
  return response.json();
};
```

### 2. Capture and Process Media

Add these functions to capture face and voice data:

```typescript
const captureImage = async (videoRef: React.RefObject<HTMLVideoElement>): Promise<Blob> => {
  return new Promise((resolve) => {
    const video = videoRef.current;
    if (!video) return;
    
    const canvas = document.createElement('canvas');
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;
    
    const ctx = canvas.getContext('2d');
    ctx?.drawImage(video, 0, 0);
    
    canvas.toBlob((blob) => {
      resolve(blob!);
    }, 'image/jpeg', 0.8);
  });
};

const recordAudio = async (duration: number = 3000): Promise<Blob> => {
  return new Promise(async (resolve, reject) => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const mediaRecorder = new MediaRecorder(stream);
      const chunks: BlobPart[] = [];
      
      mediaRecorder.ondataavailable = (event) => {
        chunks.push(event.data);
      };
      
      mediaRecorder.onstop = () => {
        const blob = new Blob(chunks, { type: 'audio/wav' });
        stream.getTracks().forEach(track => track.stop());
        resolve(blob);
      };
      
      mediaRecorder.start();
      setTimeout(() => mediaRecorder.stop(), duration);
    } catch (error) {
      reject(error);
    }
  });
};
```

### 3. Update Authentication Flow

Replace the mock authentication logic:

```typescript
const processAuthentication = async () => {
  setAuthStatus('processing');
  
  try {
    // First, register user if needed
    await registerUser(userName);
    
    // Capture face image
    const faceImage = await captureImage(videoRef);
    
    // Record voice
    const voiceAudio = await recordAudio(3000);
    
    // Check if user needs enrollment
    const statusResponse = await fetch(`${API_BASE_URL}/biometric/status/${userName}`);
    const status = await statusResponse.json();
    
    if (!status.face_enrolled) {
      await enrollFace(userName, faceImage);
    }
    
    if (!status.voice_enrolled) {
      await enrollVoice(userName, voiceAudio);
    }
    
    // Authenticate
    const authResult = await authenticateBiometric(userName, faceImage, voiceAudio);
    
    if (authResult.success) {
      setAuthStatus('success');
      setTimeout(() => onSuccess(), 2000);
    } else {
      setAuthStatus('failed');
    }
  } catch (error) {
    console.error('Authentication error:', error);
    setAuthStatus('failed');
  }
};
```

### 4. Handle CORS (if needed)

If you encounter CORS issues, the backend is already configured to allow requests from:
- `http://localhost:5173` (Vite default)
- `http://localhost:3000` (Create React App default)
- `http://127.0.0.1:5173`

## API Endpoints Reference

### Authentication
- `POST /api/auth/register` - Register user
- `GET /api/auth/user/{username}` - Get user info

### Enrollment
- `POST /api/face/enroll` - Enroll face data
- `POST /api/audio/enroll` - Enroll voice data

### Verification
- `POST /api/face/verify` - Verify face
- `POST /api/audio/verify` - Verify voice
- `POST /api/biometric/authenticate` - Combined authentication

### Status
- `GET /api/biometric/status/{username}` - Check enrollment status

## Error Handling

Add proper error handling for common scenarios:

```typescript
const handleApiError = (error: any, operation: string) => {
  console.error(`${operation} failed:`, error);
  
  if (error.message?.includes('fetch')) {
    setAuthStatus('failed');
    // Show message: "Cannot connect to server"
  } else if (error.status === 404) {
    // User not found
  } else if (error.status === 400) {
    // Bad request (invalid data)
  } else {
    // Generic error
  }
};
```

## Testing the Integration

1. Start the backend: `cd backend && python run.py`
2. Start the frontend: `npm run dev`
3. Test the flow:
   - Enter a username
   - Allow camera and microphone permissions
   - Complete the biometric authentication

## Troubleshooting

### Common Issues

1. **CORS errors**: Check that backend CORS settings include your frontend URL
2. **Camera/microphone not working**: Check browser permissions
3. **API connection failed**: Ensure backend is running on port 8000
4. **Face/voice recognition fails**: Check lighting and audio quality

### Debug Mode

Enable debug logging in the backend by setting `DEBUG=True` in `.env` file.

## Production Considerations

1. **HTTPS**: Use HTTPS in production for security
2. **Environment variables**: Set proper production URLs
3. **Rate limiting**: Add rate limiting to prevent abuse
4. **Error monitoring**: Add proper error tracking
5. **Database**: Consider using PostgreSQL instead of SQLite for production

## Next Steps

1. Test the integration thoroughly
2. Add error handling and user feedback
3. Implement proper loading states
4. Add retry mechanisms for failed authentications
5. Consider adding user management features
