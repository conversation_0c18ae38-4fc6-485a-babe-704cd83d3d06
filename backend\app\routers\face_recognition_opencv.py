"""
Alternative face recognition implementation using OpenCV only
Use this if face_recognition library installation fails
"""

from fastapi import APIRouter, HTTPException, UploadFile, File, Form, Request
from fastapi.responses import JSONResponse
import cv2
import numpy as np
from PIL import Image
import io
import json
import hashlib
from app.database import database
from app.models import users
from app.schemas import FaceRecognitionResponse, EnrollmentResponse
from app.routers.auth import log_authentication
from sqlalchemy import select, update
import base64

router = APIRouter()

# Load OpenCV face cascade classifier
face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')

def process_image_data(image_data: bytes) -> np.ndarray:
    """Convert image bytes to numpy array"""
    try:
        # Try to decode as base64 first
        if image_data.startswith(b'data:image'):
            # Remove data URL prefix
            image_data = image_data.split(b',')[1]
            image_data = base64.b64decode(image_data)
        
        # Convert to PIL Image
        image = Image.open(io.BytesIO(image_data))
        
        # Convert to RGB if necessary
        if image.mode != 'RGB':
            image = image.convert('RGB')
        
        # Convert to numpy array
        return np.array(image)
    
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Invalid image format: {str(e)}")

def extract_face_features(image: np.ndarray) -> dict:
    """Extract simple face features using OpenCV"""
    try:
        # Convert to grayscale
        gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
        
        # Detect faces
        faces = face_cascade.detectMultiScale(gray, 1.1, 4)
        
        if len(faces) == 0:
            return None
        
        # Use the largest face
        face = max(faces, key=lambda x: x[2] * x[3])
        x, y, w, h = face
        
        # Extract face region
        face_roi = gray[y:y+h, x:x+w]
        
        # Resize to standard size
        face_roi = cv2.resize(face_roi, (100, 100))
        
        # Calculate simple features
        features = {
            'face_bounds': [int(x), int(y), int(w), int(h)],
            'face_area': int(w * h),
            'aspect_ratio': float(w / h),
            'histogram': cv2.calcHist([face_roi], [0], None, [256], [0, 256]).flatten().tolist(),
            'mean_intensity': float(np.mean(face_roi)),
            'std_intensity': float(np.std(face_roi)),
        }
        
        return features
    
    except Exception as e:
        print(f"Feature extraction error: {e}")
        return None

def calculate_face_similarity(features1: dict, features2: dict) -> float:
    """Calculate similarity between two face feature sets"""
    try:
        if not features1 or not features2:
            return 0.0
        
        # Compare histograms using correlation
        hist1 = np.array(features1['histogram'])
        hist2 = np.array(features2['histogram'])
        
        correlation = cv2.compareHist(hist1.astype(np.float32), hist2.astype(np.float32), cv2.HISTCMP_CORREL)
        
        # Compare other features
        area_diff = abs(features1['face_area'] - features2['face_area']) / max(features1['face_area'], features2['face_area'])
        ratio_diff = abs(features1['aspect_ratio'] - features2['aspect_ratio'])
        intensity_diff = abs(features1['mean_intensity'] - features2['mean_intensity']) / 255.0
        
        # Weighted combination
        similarity = (
            correlation * 0.6 +
            (1 - area_diff) * 0.2 +
            (1 - min(ratio_diff, 1.0)) * 0.1 +
            (1 - intensity_diff) * 0.1
        )
        
        return max(0.0, min(1.0, similarity))
    
    except Exception as e:
        print(f"Similarity calculation error: {e}")
        return 0.0

@router.post("/enroll", response_model=EnrollmentResponse)
async def enroll_face(
    username: str = Form(...),
    image: UploadFile = File(...)
):
    """Enroll a user's face for recognition using OpenCV"""
    try:
        # Check if user exists
        query = select(users).where(users.c.username == username)
        user = await database.fetch_one(query)
        
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        # Read and process image
        image_data = await image.read()
        img_array = process_image_data(image_data)
        
        # Extract face features
        features = extract_face_features(img_array)
        
        if not features:
            return EnrollmentResponse(
                success=False,
                message="No face detected in the image"
            )
        
        # Store face features in database
        face_features_json = json.dumps(features)
        
        query = update(users).where(users.c.id == user.id).values(
            face_encoding=face_features_json
        )
        await database.execute(query)
        
        return EnrollmentResponse(
            success=True,
            message="Face enrolled successfully using OpenCV",
            user_id=user.id
        )
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Face enrollment failed: {str(e)}")

@router.post("/verify", response_model=FaceRecognitionResponse)
async def verify_face(
    username: str = Form(...),
    image: UploadFile = File(...),
    request: Request = None
):
    """Verify a user's face using OpenCV"""
    try:
        # Get user and their stored face features
        query = select(users).where(users.c.username == username)
        user = await database.fetch_one(query)
        
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        if not user.face_encoding:
            return FaceRecognitionResponse(
                success=False,
                message="No face data enrolled for this user"
            )
        
        # Read and process image
        image_data = await image.read()
        img_array = process_image_data(image_data)
        
        # Extract face features
        current_features = extract_face_features(img_array)
        
        if not current_features:
            await log_authentication(
                user.id, username, "face", False, 0.0,
                request.client.host if request else None,
                request.headers.get("user-agent") if request else None
            )
            return FaceRecognitionResponse(
                success=False,
                message="No face detected in the image"
            )
        
        # Load stored face features
        stored_features = json.loads(user.face_encoding)
        
        # Calculate similarity
        confidence = calculate_face_similarity(current_features, stored_features)
        
        # Threshold for face recognition (adjust as needed)
        threshold = 0.7  # Higher threshold for OpenCV method
        success = confidence >= threshold
        
        # Log authentication attempt
        await log_authentication(
            user.id, username, "face", success, confidence,
            request.client.host if request else None,
            request.headers.get("user-agent") if request else None
        )
        
        return FaceRecognitionResponse(
            success=success,
            message="Face verified successfully (OpenCV)" if success else "Face verification failed",
            confidence=confidence,
            user_id=user.id if success else None
        )
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Face verification failed: {str(e)}")

@router.post("/detect")
async def detect_face(image: UploadFile = File(...)):
    """Detect if there's a face in the image using OpenCV"""
    try:
        image_data = await image.read()
        img_array = process_image_data(image_data)
        
        # Convert to grayscale
        gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
        
        # Detect faces
        faces = face_cascade.detectMultiScale(gray, 1.1, 4)
        
        face_locations = []
        for (x, y, w, h) in faces:
            face_locations.append([int(y), int(x+w), int(y+h), int(x)])  # Convert to face_recognition format
        
        return {
            "faces_detected": len(faces),
            "face_locations": face_locations,
            "message": f"Detected {len(faces)} face(s) using OpenCV" if faces.size > 0 else "No faces detected"
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Face detection failed: {str(e)}")
