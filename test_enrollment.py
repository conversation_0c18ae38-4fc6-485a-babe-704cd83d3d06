#!/usr/bin/env python3
"""
Test script to verify enrollment endpoints are working
"""

import requests
import json

def test_backend_endpoints():
    """Test the backend enrollment endpoints"""
    base_url = "http://127.0.0.1:8000"
    
    print("🧪 Testing Backend Enrollment Endpoints")
    print("=" * 50)
    
    # Test 1: Health check
    try:
        response = requests.get(f"{base_url}/health")
        print(f"✅ Health Check: {response.status_code} - {response.json()}")
    except Exception as e:
        print(f"❌ Health Check Failed: {e}")
        return
    
    # Test 2: Check authorized users
    try:
        response = requests.get(f"{base_url}/api/auth/authorized-users")
        data = response.json()
        print(f"✅ Authorized Users: {response.status_code}")
        print(f"   Users: {[user['username'] for user in data['authorized_users']]}")
        print(f"   Registered: {data['registered_users']}/{data['total_users']}")
    except Exception as e:
        print(f"❌ Authorized Users Failed: {e}")
    
    # Test 3: Try to register a user
    try:
        user_data = {
            "username": "fenny_mary",
            "email": "<EMAIL>"
        }
        response = requests.post(
            f"{base_url}/api/auth/register",
            json=user_data,
            headers={"Content-Type": "application/json"}
        )
        print(f"✅ User Registration: {response.status_code} - {response.json()}")
    except Exception as e:
        print(f"❌ User Registration Failed: {e}")
    
    # Test 4: Try to login
    try:
        login_data = {
            "username": "fenny_mary"
        }
        response = requests.post(
            f"{base_url}/api/auth/login",
            json=login_data,
            headers={"Content-Type": "application/json"}
        )
        print(f"✅ User Login: {response.status_code} - {response.json()}")
    except Exception as e:
        print(f"❌ User Login Failed: {e}")
    
    # Test 5: Get user info
    try:
        response = requests.get(f"{base_url}/api/auth/user/fenny_mary")
        print(f"✅ User Info: {response.status_code} - {response.json()}")
    except Exception as e:
        print(f"❌ User Info Failed: {e}")
    
    print("\n🎯 Enrollment Endpoint Status:")
    print("   Face Enrollment: POST /api/face/enroll")
    print("   Voice Enrollment: POST /api/biometric/enroll-voice")
    print("   Face Verification: POST /api/face/verify-face")
    print("   Voice Verification: POST /api/biometric/verify-voice")

def test_database_status():
    """Test database status"""
    import sqlite3
    import os
    
    print("\n🗄️ Testing Database Status")
    print("=" * 50)
    
    db_path = "backend/biometric_auth.db"
    
    if not os.path.exists(db_path):
        print(f"❌ Database not found: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check users table
        cursor.execute("SELECT username, email, face_encoding, voice_print FROM users")
        users = cursor.fetchall()
        
        print(f"✅ Database connected: {len(users)} users found")
        for user in users:
            username, email, face, voice = user
            print(f"   👤 {username} ({email})")
            print(f"      Face: {'✅ Enrolled' if face else '❌ Not enrolled'}")
            print(f"      Voice: {'✅ Enrolled' if voice else '❌ Not enrolled'}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Database error: {e}")

if __name__ == "__main__":
    test_backend_endpoints()
    test_database_status()
    
    print("\n📋 Next Steps:")
    print("1. Open http://localhost:8082 in browser")
    print("2. Select 'fenny_mary', 'george_bobby', or 'joyal_antony'")
    print("3. Complete face enrollment (capture face)")
    print("4. Complete voice enrollment (record voice)")
    print("5. Test authentication")
    
    print("\n🔧 Troubleshooting:")
    print("- Check browser console for detailed logs")
    print("- Ensure camera and microphone permissions are granted")
    print("- Audio should record for 5 seconds and stop automatically")
    print("- Face and voice data should be saved to backend database")
