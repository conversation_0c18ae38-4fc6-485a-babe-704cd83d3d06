#!/usr/bin/env python3
"""
Check database contents and fix any issues
"""

import sqlite3
import os
from datetime import datetime

def check_database():
    """Check the database contents"""
    db_path = "db.sqlite3"
    
    if not os.path.exists(db_path):
        print(f"❌ Database file {db_path} does not exist!")
        return False
    
    print(f"✅ Database file found: {db_path}")
    
    # Connect to database
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Check tables
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
    tables = cursor.fetchall()
    print(f"📊 Tables in database: {[table[0] for table in tables]}")
    
    # Check users table
    try:
        cursor.execute("SELECT COUNT(*) FROM users")
        user_count = cursor.fetchone()[0]
        print(f"👥 Users in database: {user_count}")
        
        if user_count > 0:
            cursor.execute("SELECT id, username, email, face_encoding, voice_print FROM users")
            users = cursor.fetchall()
            print("\n📋 User Details:")
            for user in users:
                print(f"  ID: {user[0]}, Username: {user[1]}, Email: {user[2]}")
                print(f"      Face: {'✅' if user[3] else '❌'}, Voice: {'✅' if user[4] else '❌'}")
        else:
            print("⚠️  No users found in database!")
            
    except Exception as e:
        print(f"❌ Error checking users table: {e}")
    
    conn.close()
    return True

def fix_database():
    """Fix database by adding team members"""
    db_path = "db.sqlite3"
    
    # Team member details
    TEAM_MEMBERS = [
        {
            "username": "team_member_1",
            "email": "<EMAIL>",
        },
        {
            "username": "team_member_2", 
            "email": "<EMAIL>",
        },
        {
            "username": "team_member_3",
            "email": "<EMAIL>",
        }
    ]
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Create users table if it doesn't exist
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username VARCHAR(50) UNIQUE NOT NULL,
            email VARCHAR(100) UNIQUE,
            face_encoding TEXT,
            voice_print TEXT,
            is_active BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    """)
    
    # Clear and add team members
    cursor.execute("DELETE FROM users")
    
    for member in TEAM_MEMBERS:
        cursor.execute("""
            INSERT INTO users (username, email, is_active, created_at, updated_at)
            VALUES (?, ?, 1, ?, ?)
        """, (
            member["username"],
            member["email"], 
            datetime.now().isoformat(),
            datetime.now().isoformat()
        ))
        print(f"✅ Added: {member['username']}")
    
    conn.commit()
    conn.close()
    
    print("🎉 Database fixed!")

if __name__ == "__main__":
    print("🔍 Checking Database Status")
    print("=" * 40)
    
    check_database()
    
    print("\n🔧 Fixing Database")
    print("=" * 40)
    
    fix_database()
    
    print("\n🔍 Checking Database Again")
    print("=" * 40)
    
    check_database()
