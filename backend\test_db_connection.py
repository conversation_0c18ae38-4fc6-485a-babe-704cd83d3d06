#!/usr/bin/env python3
"""
Test database connection and query
"""

import asyncio
import os
import sys

# Add the current directory to the path so we can import app modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.database import database, DATABASE_URL
from app.models import users
from sqlalchemy import select

async def test_database():
    """Test database connection and queries"""
    print(f"🔗 Database URL: {DATABASE_URL}")
    
    try:
        # Connect to database
        await database.connect()
        print("✅ Database connected successfully")
        
        # Test query
        query = select(users)
        result = await database.fetch_all(query)
        
        print(f"👥 Found {len(result)} users in database:")
        for user in result:
            print(f"  - {user['username']} ({user['email']})")
            print(f"    Face: {'✅' if user['face_encoding'] else '❌'}")
            print(f"    Voice: {'✅' if user['voice_print'] else '❌'}")
        
        # Disconnect
        await database.disconnect()
        print("✅ Database disconnected successfully")
        
    except Exception as e:
        print(f"❌ Database error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_database())
