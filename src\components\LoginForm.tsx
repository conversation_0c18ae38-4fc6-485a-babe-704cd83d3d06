
import { useState } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Shield, UserCheck, UserPlus, Loader2, AlertCircle } from 'lucide-react';
import { apiService } from '@/services/api';

interface LoginFormProps {
  onSubmit: (name: string) => void;
}

const LoginForm = ({ onSubmit }: LoginFormProps) => {
  const [username, setUsername] = useState('');
  const [email, setEmail] = useState('');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [mode, setMode] = useState<'login' | 'register'>('login');
  const [userInfo, setUserInfo] = useState<any>(null);
  const [showAuthorizedUsers, setShowAuthorizedUsers] = useState(false);

  // Authorized team members
  const AUTHORIZED_USERS = [
    { username: 'fenny_mary', display: 'Fenny Mary' },
    { username: 'george_bobby', display: 'George Bobby' },
    { username: 'joyal_antony', display: 'Joyal Antony' }
  ];

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!username.trim()) {
      setError('Please enter your username');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      const result = await apiService.loginUser({ username: username.trim() });

      if (result.success) {
        setUserInfo(result);
        onSubmit(username.trim());
      } else {
        setError(result.message || 'Login failed');
      }
    } catch (error) {
      console.error('Login error:', error);
      setError(error instanceof Error ? error.message : 'Login failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!username.trim()) {
      setError('Please enter a username');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      const result = await apiService.registerUser({
        username: username.trim(),
        email: email.trim() || undefined
      });

      if (result.success) {
        setUserInfo(result);
        // After successful registration, proceed to biometric setup
        onSubmit(username.trim());
      } else {
        setError(result.message || 'Registration failed');
      }
    } catch (error) {
      console.error('Registration error:', error);
      setError(error instanceof Error ? error.message : 'Registration failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const switchMode = () => {
    setMode(mode === 'login' ? 'register' : 'login');
    setError('');
    setEmail('');
  };

  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, y: 50 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, ease: "easeOut" }}
      >
        <Card className="w-full max-w-md bg-white/10 backdrop-blur-lg border-white/20 shadow-2xl">
          <CardHeader className="text-center">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.3, type: "spring", stiffness: 200 }}
              className="mx-auto mb-4 w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center"
            >
              <Shield className="w-8 h-8 text-white" />
            </motion.div>
            <CardTitle className="text-2xl font-bold text-white">
              {mode === 'login' ? 'Secure Access Portal' : 'User Registration'}
            </CardTitle>
            <CardDescription className="text-gray-300">
              {mode === 'login'
                ? 'Enter your username to begin biometric authentication'
                : 'Create a new account for biometric authentication'
              }
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={mode === 'login' ? handleLogin : handleRegister} className="space-y-6">
              <div className="space-y-4">
                <div className="space-y-2">
                  <label htmlFor="username" className="text-sm font-medium text-white">
                    Username
                  </label>
                  <Input
                    id="username"
                    type="text"
                    placeholder="Enter your username"
                    value={username}
                    onChange={(e) => setUsername(e.target.value)}
                    className="bg-white/10 border-white/20 text-white placeholder:text-gray-400 focus:ring-2 focus:ring-blue-500"
                    disabled={isLoading}
                  />

                  {/* Show authorized users */}
                  <div className="mt-2">
                    <button
                      type="button"
                      onClick={() => setShowAuthorizedUsers(!showAuthorizedUsers)}
                      className="text-xs text-blue-400 hover:text-blue-300 underline"
                    >
                      {showAuthorizedUsers ? 'Hide' : 'Show'} authorized team members
                    </button>

                    {showAuthorizedUsers && (
                      <motion.div
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: 'auto' }}
                        className="mt-2 p-2 bg-white/5 rounded border border-white/10"
                      >
                        <p className="text-xs text-gray-400 mb-2">Authorized team members:</p>
                        <div className="space-y-1">
                          {AUTHORIZED_USERS.map((user) => (
                            <button
                              key={user.username}
                              type="button"
                              onClick={() => setUsername(user.username)}
                              className="block w-full text-left text-xs text-blue-300 hover:text-blue-200 hover:bg-white/5 px-2 py-1 rounded"
                            >
                              {user.username} ({user.display})
                            </button>
                          ))}
                        </div>
                      </motion.div>
                    )}
                  </div>
                </div>

                {mode === 'register' && (
                  <div className="space-y-2">
                    <label htmlFor="email" className="text-sm font-medium text-white">
                      Email (Optional)
                    </label>
                    <Input
                      id="email"
                      type="email"
                      placeholder="Enter your email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      className="bg-white/10 border-white/20 text-white placeholder:text-gray-400 focus:ring-2 focus:ring-blue-500"
                      disabled={isLoading}
                    />
                  </div>
                )}

                {error && (
                  <motion.div
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    className="p-3 bg-red-500/10 border border-red-500/20 rounded-lg"
                  >
                    <div className="flex items-center gap-2 text-red-400">
                      <AlertCircle className="w-4 h-4" />
                      <span className="text-sm">{error}</span>
                    </div>
                  </motion.div>
                )}
              </div>

              <div className="space-y-3">
                <Button
                  type="submit"
                  disabled={isLoading}
                  className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold py-2 px-4 rounded-lg transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:transform-none"
                >
                  {isLoading ? (
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  ) : mode === 'login' ? (
                    <UserCheck className="w-4 h-4 mr-2" />
                  ) : (
                    <UserPlus className="w-4 h-4 mr-2" />
                  )}
                  {mode === 'login' ? 'Proceed to Verification' : 'Create Account'}
                </Button>

                <Button
                  type="button"
                  variant="ghost"
                  onClick={switchMode}
                  disabled={isLoading}
                  className="w-full text-gray-300 hover:text-white hover:bg-white/10"
                >
                  {mode === 'login'
                    ? "Don't have an account? Register here"
                    : "Already have an account? Login here"
                  }
                </Button>
              </div>
            </form>

            {userInfo && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                className="mt-6 p-4 bg-green-500/10 border border-green-500/20 rounded-lg"
              >
                <h4 className="text-sm font-medium text-green-400 mb-2">
                  {mode === 'login' ? 'Login Successful!' : 'Registration Successful!'}
                </h4>
                <div className="text-xs text-gray-300 space-y-1">
                  <div>Username: {userInfo.username}</div>
                  {userInfo.has_face !== undefined && (
                    <div>Face enrolled: {userInfo.has_face ? '✓' : '✗'}</div>
                  )}
                  {userInfo.has_voice !== undefined && (
                    <div>Voice enrolled: {userInfo.has_voice ? '✓' : '✗'}</div>
                  )}
                </div>
              </motion.div>
            )}

            <div className="mt-6 p-4 bg-white/5 rounded-lg border border-white/10">
              <h4 className="text-sm font-medium text-white mb-2">How it works:</h4>
              <ul className="text-xs text-gray-300 space-y-1">
                <li className="flex items-center">
                  <div className="w-2 h-2 bg-blue-400 rounded-full mr-2"></div>
                  {mode === 'login' ? 'Login with your username' : 'Create a new account'}
                </li>
                <li className="flex items-center">
                  <div className="w-2 h-2 bg-blue-400 rounded-full mr-2"></div>
                  Complete face recognition setup
                </li>
                <li className="flex items-center">
                  <div className="w-2 h-2 bg-blue-400 rounded-full mr-2"></div>
                  Complete voice recognition setup
                </li>
                <li className="flex items-center">
                  <div className="w-2 h-2 bg-green-400 rounded-full mr-2"></div>
                  Access secure dashboard
                </li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
};

export default LoginForm;
