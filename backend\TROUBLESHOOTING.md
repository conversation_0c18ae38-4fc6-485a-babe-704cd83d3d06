# Troubleshooting Guide

This guide helps resolve common installation and dependency issues with the Persona Audio Gatekeeper Backend.

## Quick Start (Recommended)

### Windows Users
```bash
cd backend
install_windows.bat
```

### All Platforms
```bash
cd backend
python install_dependencies.py
```

## Common Issues and Solutions

### 1. face_recognition Package Issues

**Problem**: `face_recognition` package fails to install or import
**Error messages**:
- "dlib is not installed"
- "Microsoft Visual C++ 14.0 is required"
- "cmake is not found"

**Solutions**:

#### Option A: Install Prerequisites (Windows)
1. Install Visual Studio Build Tools:
   - Download from: https://visualstudio.microsoft.com/visual-cpp-build-tools/
   - Install "C++ build tools" workload

2. Install CMake:
   - Download from: https://cmake.org/download/
   - Add to PATH during installation

3. Restart your terminal and try again:
   ```bash
   pip install cmake
   pip install dlib
   pip install face_recognition
   ```

#### Option B: Use Precompiled Wheels
```bash
# Try installing from conda-forge
conda install -c conda-forge dlib
pip install face_recognition
```

#### Option C: Use OpenCV Fallback
If face_recognition still fails, the backend automatically falls back to OpenCV-only face detection:

1. Edit `.env` file:
   ```
   USE_FACE_RECOGNITION_LIB=false
   ```

2. Install minimal requirements:
   ```bash
   pip install -r requirements_minimal.txt
   ```

### 2. PyAudio Issues

**Problem**: PyAudio fails to install
**Error messages**:
- "Microsoft Visual C++ 14.0 is required"
- "portaudio.h: No such file or directory"

**Solutions**:

#### Windows:
```bash
# Option 1: Use pipwin
pip install pipwin
pipwin install pyaudio

# Option 2: Download precompiled wheel
# Visit: https://www.lfd.uci.edu/~gohlke/pythonlibs/#pyaudio
# Download appropriate .whl file and install:
pip install downloaded_wheel.whl
```

#### macOS:
```bash
brew install portaudio
pip install pyaudio
```

#### Linux (Ubuntu/Debian):
```bash
sudo apt-get install portaudio19-dev python3-pyaudio
pip install pyaudio
```

#### Fallback (Limited Audio):
If PyAudio installation fails, edit `.env`:
```
USE_PYAUDIO=false
```

### 3. OpenCV Issues

**Problem**: OpenCV fails to install or import
**Solutions**:
```bash
# Try different OpenCV packages
pip uninstall opencv-python opencv-contrib-python
pip install opencv-python-headless==********

# Or try the full version
pip install opencv-contrib-python==********
```

### 4. Virtual Environment Issues

**Problem**: Packages not found even after installation
**Solution**: Make sure virtual environment is activated:

#### Windows:
```bash
venv\Scripts\activate
```

#### macOS/Linux:
```bash
source venv/bin/activate
```

### 5. Permission Issues

**Problem**: Permission denied during installation
**Solutions**:

#### Windows:
- Run Command Prompt as Administrator
- Or use `--user` flag: `pip install --user package_name`

#### macOS/Linux:
- Use `sudo` for system packages: `sudo apt-get install ...`
- Use `--user` flag for pip: `pip install --user package_name`

## Alternative Installation Methods

### Method 1: Conda Environment
```bash
# Create conda environment
conda create -n persona-backend python=3.9
conda activate persona-backend

# Install packages via conda
conda install -c conda-forge dlib opencv
conda install -c conda-forge pyaudio

# Install remaining packages via pip
pip install fastapi uvicorn face_recognition
```

### Method 2: Docker (Advanced)
Create a `Dockerfile`:
```dockerfile
FROM python:3.9-slim

RUN apt-get update && apt-get install -y \
    cmake \
    build-essential \
    libopencv-dev \
    portaudio19-dev \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
CMD ["python", "run.py"]
```

### Method 3: System Package Manager

#### Ubuntu/Debian:
```bash
sudo apt-get update
sudo apt-get install python3-opencv python3-pyaudio
pip install face_recognition fastapi uvicorn
```

#### macOS with Homebrew:
```bash
brew install opencv python-tk portaudio
pip install face_recognition fastapi uvicorn pyaudio
```

## Testing Your Installation

### 1. Test Individual Components
```bash
# Test face recognition
python -c "import face_recognition; print('✅ face_recognition OK')"

# Test OpenCV
python -c "import cv2; print('✅ OpenCV OK')"

# Test PyAudio
python -c "import pyaudio; print('✅ PyAudio OK')"

# Test FastAPI
python -c "import fastapi; print('✅ FastAPI OK')"
```

### 2. Run the Test Script
```bash
python test_api.py
```

### 3. Check Configuration
```bash
python -c "from app.config import *; print('✅ Configuration loaded')"
```

## Environment-Specific Solutions

### Windows 10/11
- Install Windows Subsystem for Linux (WSL) for better compatibility
- Use Anaconda/Miniconda for easier package management
- Consider using Docker Desktop

### macOS
- Install Xcode Command Line Tools: `xcode-select --install`
- Use Homebrew for system dependencies
- Consider using pyenv for Python version management

### Linux
- Install development packages: `sudo apt-get install build-essential`
- Use system package manager when possible
- Consider using virtual environments or conda

## Getting Help

### 1. Check Logs
Look for detailed error messages in the terminal output.

### 2. Verify System Requirements
- Python 3.8 or higher
- At least 2GB RAM
- Working camera and microphone (for testing)

### 3. Common Error Patterns

#### "No module named 'app'"
- Make sure you're in the `backend` directory
- Activate virtual environment

#### "Database connection failed"
- Check if SQLite is available
- Verify database file permissions

#### "CORS error"
- Check frontend URL in ALLOWED_ORIGINS
- Verify both frontend and backend are running

### 4. Reset Installation
If all else fails:
```bash
# Remove virtual environment
rm -rf venv

# Remove any cached files
rm -rf __pycache__ app/__pycache__

# Start fresh
python setup.py
```

## Production Deployment

For production deployment, consider:
1. Using Docker containers
2. Installing system packages via package manager
3. Using a process manager like systemd or supervisor
4. Setting up proper logging and monitoring

## Contact and Support

If you continue to have issues:
1. Check the GitHub issues for similar problems
2. Provide full error messages and system information
3. Include Python version and operating system details
