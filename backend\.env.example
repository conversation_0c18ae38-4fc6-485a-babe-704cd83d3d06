# Database Configuration
DATABASE_URL=sqlite:///./biometric_auth.db

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
DEBUG=True

# Security
SECRET_KEY=your-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Face Recognition Settings
FACE_RECOGNITION_THRESHOLD=0.6

# Voice Recognition Settings
VOICE_RECOGNITION_THRESHOLD=0.6

# CORS Settings
ALLOWED_ORIGINS=http://localhost:5173,http://localhost:3000,http://127.0.0.1:5173

# Feature flags (set to false if packages are not available)
USE_FACE_RECOGNITION_LIB=true
USE_PYAUDIO=true
