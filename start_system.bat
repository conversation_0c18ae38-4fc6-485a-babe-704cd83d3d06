@echo off
echo ========================================
echo  Persona Audio Gatekeeper - Quick Start
echo ========================================
echo.

echo 🚀 Starting Backend Server...
cd backend
start "Backend Server" cmd /k "venv\Scripts\python.exe run.py"
cd ..

echo.
echo ⏳ Waiting for backend to initialize...
timeout /t 5 /nobreak > nul

echo.
echo 🎨 Starting Frontend Server...
start "Frontend Server" cmd /k "npm run dev"

echo.
echo ✅ Both servers are starting!
echo.
echo 📋 Team Member Instructions:
echo 1. Open http://localhost:5173 in your browser
echo 2. Click 'Show authorized team members'
echo 3. Select your username:
echo    • team_member_1
echo    • team_member_2
echo    • team_member_3
echo 4. Complete biometric enrollment
echo 5. Test authentication
echo.
echo 🔧 System URLs:
echo • Frontend: http://localhost:5173
echo • Backend API: http://127.0.0.1:8000
echo • API Documentation: http://127.0.0.1:8000/docs
echo.
echo ⚠️  Close the server windows to stop the system
echo.
pause
