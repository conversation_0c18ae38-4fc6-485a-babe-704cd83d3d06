#!/usr/bin/env python3
"""
Fix the database by directly populating biometric_auth.db
"""

import sqlite3
import os
from datetime import datetime

def fix_database():
    """Fix database by adding team members to biometric_auth.db"""
    db_path = "biometric_auth.db"
    
    # Team member details
    TEAM_MEMBERS = [
        {
            "username": "team_member_1",
            "email": "<EMAIL>",
        },
        {
            "username": "team_member_2", 
            "email": "<EMAIL>",
        },
        {
            "username": "team_member_3",
            "email": "<EMAIL>",
        }
    ]
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Create users table if it doesn't exist
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username VARCHAR(50) UNIQUE NOT NULL,
            email VARCHAR(100) UNIQUE,
            face_encoding TEXT,
            voice_print TEXT,
            is_active BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    """)
    
    # Create authentication_logs table if it doesn't exist
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS authentication_logs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            username VARCHAR(50) NOT NULL,
            auth_type VARCHAR(20) NOT NULL,
            success BOOLEAN NOT NULL,
            confidence_score VARCHAR(10),
            ip_address VARCHAR(45),
            user_agent VARCHAR(500),
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    """)
    
    # Clear existing data for fresh start
    cursor.execute("DELETE FROM users")
    cursor.execute("DELETE FROM authentication_logs")
    
    # Insert team members
    for member in TEAM_MEMBERS:
        cursor.execute("""
            INSERT INTO users (username, email, is_active, created_at, updated_at)
            VALUES (?, ?, 1, ?, ?)
        """, (
            member["username"],
            member["email"], 
            datetime.now().isoformat(),
            datetime.now().isoformat()
        ))
        print(f"✅ Added: {member['username']}")
    
    conn.commit()
    
    # Verify users
    cursor.execute("SELECT username, email FROM users")
    users = cursor.fetchall()
    
    print(f"\n📊 Database fixed with {len(users)} team members:")
    for user in users:
        print(f"   • {user[0]} ({user[1]})")
    
    conn.close()
    return True

if __name__ == "__main__":
    print("🔧 Fixing biometric_auth.db Database")
    print("=" * 40)
    
    fix_database()
    
    print("\n🎉 Database fixed successfully!")
