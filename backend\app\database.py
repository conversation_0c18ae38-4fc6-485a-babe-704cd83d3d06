# ✅ database.py
from databases import Database
from sqlalchemy import create_engine, MetaData
import os
from sqlalchemy.ext.declarative import declarative_base


DATABASE_URL = os.getenv("DATABASE_URL", "sqlite+aiosqlite:///./db.sqlite3")

database = Database(DATABASE_URL)
engine = create_engine(DATABASE_URL.replace("+aiosqlite", ""))
metadata = MetaData()

# Bind metadata to the engine
metadata.bind = engine
Base = declarative_base()
