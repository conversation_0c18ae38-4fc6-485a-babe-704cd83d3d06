# ✅ database.py
from databases import Database
from sqlalchemy import create_engine, MetaData
import os
from sqlalchemy.ext.declarative import declarative_base


# Get the absolute path to the database file
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
DB_PATH = os.path.join(BASE_DIR, "db.sqlite3")
DATABASE_URL = os.getenv("DATABASE_URL", f"sqlite+aiosqlite:///{DB_PATH}")

database = Database(DATABASE_URL)
engine = create_engine(DATABASE_URL.replace("+aiosqlite", "").replace("///", "///"))
metadata = MetaData()

# Bind metadata to the engine
metadata.bind = engine
Base = declarative_base()
