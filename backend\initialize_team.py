#!/usr/bin/env python3
"""
Initialize the 3 team members in the database
Run this script to set up your team for biometric authentication
"""

import asyncio
import sqlite3
from datetime import datetime
import os

# Team member details
TEAM_MEMBERS = [
    {
        "username": "team_member_1",
        "email": "<EMAIL>",
        "display_name": "Team Member 1"
    },
    {
        "username": "team_member_2", 
        "email": "<EMAIL>",
        "display_name": "Team Member 2"
    },
    {
        "username": "team_member_3",
        "email": "<EMAIL>", 
        "display_name": "Team Member 3"
    }
]

def initialize_database():
    """Initialize the database with team members"""
    
    # Create database file if it doesn't exist
    db_path = "db.sqlite3"
    
    # Connect to database
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Create users table if it doesn't exist
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username VARCHAR(50) UNIQUE NOT NULL,
            email VARCHAR(100) UNIQUE,
            face_encoding TEXT,
            voice_print TEXT,
            is_active BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    """)
    
    # Create authentication_logs table if it doesn't exist
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS authentication_logs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            username VARCHAR(50) NOT NULL,
            auth_type VARCHAR(20) NOT NULL,
            success BOOLEAN NOT NULL,
            confidence_score VARCHAR(10),
            ip_address VARCHAR(45),
            user_agent VARCHAR(500),
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    """)
    
    # Clear existing users (for fresh start)
    print("🗑️  Clearing existing users...")
    cursor.execute("DELETE FROM users")
    cursor.execute("DELETE FROM authentication_logs")
    
    # Insert team members
    print("👥 Adding team members...")
    for member in TEAM_MEMBERS:
        try:
            cursor.execute("""
                INSERT INTO users (username, email, is_active, created_at, updated_at)
                VALUES (?, ?, 1, ?, ?)
            """, (
                member["username"],
                member["email"], 
                datetime.now().isoformat(),
                datetime.now().isoformat()
            ))
            print(f"✅ Added: {member['display_name']} ({member['username']})")
        except sqlite3.IntegrityError as e:
            print(f"⚠️  User {member['username']} already exists: {e}")
    
    # Commit changes
    conn.commit()
    
    # Verify users were added
    cursor.execute("SELECT id, username, email, created_at FROM users")
    users = cursor.fetchall()
    
    print("\n📋 Current users in database:")
    print("-" * 60)
    for user in users:
        print(f"ID: {user[0]} | Username: {user[1]} | Email: {user[2]}")
    
    conn.close()
    
    print(f"\n🎉 Database initialized successfully!")
    print(f"📁 Database location: {os.path.abspath(db_path)}")
    
    return True

def create_directories():
    """Create necessary directories for face storage"""
    directories = [
        "registered_faces",
        "temp"
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"📁 Created directory: {directory}")

def main():
    """Main initialization function"""
    print("🚀 Initializing Persona Audio Gatekeeper for Team")
    print("=" * 60)
    
    # Create necessary directories
    create_directories()
    
    # Initialize database
    initialize_database()
    
    print("\n📝 Next Steps:")
    print("1. Start the backend: python run.py")
    print("2. Start the frontend: npm run dev")
    print("3. Each team member should:")
    print("   a) Login with their username")
    print("   b) Complete face enrollment (capture face)")
    print("   c) Complete voice enrollment (record voice)")
    print("   d) Test authentication")
    
    print("\n👥 Team Member Usernames:")
    for i, member in enumerate(TEAM_MEMBERS, 1):
        print(f"   {i}. {member['username']} ({member['display_name']})")
    
    print("\n🔐 Authentication Flow:")
    print("   Login → Face Enrollment → Voice Enrollment → Verification")

if __name__ == "__main__":
    main()
