# ✅ app/models.py
from sqlalchemy import Table, Column, Integer, String, DateTime, Text, Boolean
from sqlalchemy.sql import func
from app.database import metadata

# Users Table
users = Table(
    "users",
    metadata,
    Column("id", Integer, primary_key=True, index=True),
    <PERSON>umn("username", String(50), unique=True, index=True, nullable=False),
    Column("email", String(100), unique=True, index=True),
    <PERSON>umn("face_encoding", Text),
    <PERSON>umn("voice_print", Text),
    <PERSON>umn("is_active", Boolean, default=True),
    Column("created_at", DateTime(timezone=True), server_default=func.now()),
    Column("updated_at", DateTime(timezone=True), onupdate=func.now()),
)

# Authentication Logs Table
authentication_logs = Table(
    "authentication_logs",
    metadata,
    Column("id", Integer, primary_key=True, index=True),
    <PERSON>umn("user_id", Integer, nullable=False),
    <PERSON>umn("username", String(50), nullable=False),
    <PERSON>umn("auth_type", String(20), nullable=False),  # 'face', 'voice', 'combined'
    Column("success", Boolean, nullable=False),
    Column("confidence_score", String(10)),
    Column("ip_address", String(45)),
    Column("user_agent", String(500)),
    Column("timestamp", DateTime(timezone=True), server_default=func.now()),
)
