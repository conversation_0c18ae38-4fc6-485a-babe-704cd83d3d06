from fastapi import APIRouter, HTTPException, UploadFile, File, Form, Request
from fastapi.responses import JSONResponse
from app.database import database
from app.models import users
from app.routers.auth import log_authentication
from app.utils.audio_utils import extract_mfcc_features, cosine_similarity
from sqlalchemy import select, update
import numpy as np
import json
import io

router = APIRouter()

@router.post("/enroll-voice")
async def enroll_voice(username: str = Form(...), audio: UploadFile = File(...)):
    query = select(users).where(users.c.username == username)
    user = await database.fetch_one(query)

    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    audio_data = await audio.read()
    features = extract_mfcc_features(io.BytesIO(audio_data))
    if features is None:
        raise HTTPException(status_code=400, detail="Invalid audio")

    features_json = json.dumps(features.tolist())
    query = update(users).where(users.c.id == user.id).values(voice_print=features_json)
    await database.execute(query)

    return {"success": True, "message": "Voice enrolled", "user_id": user.id}

@router.post("/verify-voice")
async def verify_voice(username: str = Form(...), audio: UploadFile = File(...), request: Request = None):
    query = select(users).where(users.c.username == username)
    user = await database.fetch_one(query)

    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    if not user.voice_print:
        return JSONResponse(content={"success": False, "message": "No enrolled voice"})

    audio_data = await audio.read()
    test_features = extract_mfcc_features(io.BytesIO(audio_data))
    if test_features is None:
        return JSONResponse(content={"success": False, "message": "Invalid audio"})

    stored_features = np.array(json.loads(user.voice_print))
    confidence = cosine_similarity(test_features, stored_features)
    threshold = 0.7
    success = confidence >= threshold

    await log_authentication(
        user.id, username, "voice", success, confidence,
        request.client.host if request else None,
        request.headers.get("user-agent") if request else None
    )

    return JSONResponse(content={
        "success": success,
        "message": "Voice verified" if success else "Voice verification failed",
        "confidence": confidence,
        "user_id": user.id if success else None
    })