#!/usr/bin/env python3
"""
Complete Setup Script for Persona Audio Gatekeeper Team Authentication
This script will:
1. Initialize the database with your 3 team members
2. Create necessary directories
3. Start the backend server
4. Provide instructions for frontend setup
"""

import os
import sys
import subprocess
import sqlite3
from datetime import datetime
import time

def print_header(title):
    """Print a formatted header"""
    print("\n" + "="*60)
    print(f"🚀 {title}")
    print("="*60)

def print_step(step, description):
    """Print a formatted step"""
    print(f"\n📋 Step {step}: {description}")
    print("-" * 40)

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        sys.exit(1)
    print(f"✅ Python {sys.version.split()[0]} detected")

def create_directories():
    """Create necessary directories"""
    directories = [
        "backend/registered_faces",
        "backend/temp",
        "backend/logs"
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"📁 Created: {directory}")

def initialize_database():
    """Initialize SQLite database with team members"""
    
    # Team member details
    TEAM_MEMBERS = [
        {
            "username": "fenny_mary",
            "email": "<EMAIL>",
            "display_name": "Fenny Mary"
        },
        {
            "username": "george_bobby",
            "email": "<EMAIL>",
            "display_name": "George Bobby"
        },
        {
            "username": "joyal_antony",
            "email": "<EMAIL>",
            "display_name": "Joyal Antony"
        }
    ]
    
    db_path = "backend/db.sqlite3"
    
    # Connect to database
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Create users table
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username VARCHAR(50) UNIQUE NOT NULL,
            email VARCHAR(100) UNIQUE,
            face_encoding TEXT,
            voice_print TEXT,
            is_active BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    """)
    
    # Create authentication_logs table
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS authentication_logs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            username VARCHAR(50) NOT NULL,
            auth_type VARCHAR(20) NOT NULL,
            success BOOLEAN NOT NULL,
            confidence_score VARCHAR(10),
            ip_address VARCHAR(45),
            user_agent VARCHAR(500),
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    """)
    
    # Clear existing data for fresh start
    cursor.execute("DELETE FROM users")
    cursor.execute("DELETE FROM authentication_logs")
    
    # Insert team members
    for member in TEAM_MEMBERS:
        cursor.execute("""
            INSERT INTO users (username, email, is_active, created_at, updated_at)
            VALUES (?, ?, 1, ?, ?)
        """, (
            member["username"],
            member["email"], 
            datetime.now().isoformat(),
            datetime.now().isoformat()
        ))
        print(f"✅ Added: {member['display_name']} ({member['username']})")
    
    conn.commit()
    
    # Verify users
    cursor.execute("SELECT username, email FROM users")
    users = cursor.fetchall()
    
    print(f"\n📊 Database initialized with {len(users)} team members:")
    for user in users:
        print(f"   • {user[0]} ({user[1]})")
    
    conn.close()
    return True

def install_backend_dependencies():
    """Install Python backend dependencies"""
    try:
        os.chdir("backend")
        
        # Check if virtual environment exists
        if not os.path.exists("venv"):
            print("🔧 Creating virtual environment...")
            subprocess.run([sys.executable, "-m", "venv", "venv"], check=True)
        
        # Activate virtual environment and install dependencies
        if os.name == 'nt':  # Windows
            pip_path = "venv\\Scripts\\pip.exe"
            python_path = "venv\\Scripts\\python.exe"
        else:  # Unix/Linux/Mac
            pip_path = "venv/bin/pip"
            python_path = "venv/bin/python"
        
        print("📦 Installing backend dependencies...")
        subprocess.run([pip_path, "install", "-r", "requirements.txt"], check=True)
        
        os.chdir("..")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install backend dependencies: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def check_frontend_dependencies():
    """Check if frontend dependencies are installed"""
    if os.path.exists("node_modules") and os.path.exists("package.json"):
        print("✅ Frontend dependencies appear to be installed")
        return True
    else:
        print("⚠️  Frontend dependencies not found. Please run 'npm install' in the root directory")
        return False

def main():
    """Main setup function"""
    print_header("Persona Audio Gatekeeper Team Setup")
    
    # Check Python version
    print_step(1, "Checking Python Version")
    check_python_version()
    
    # Create directories
    print_step(2, "Creating Directories")
    create_directories()
    
    # Initialize database
    print_step(3, "Initializing Database")
    initialize_database()
    
    # Install backend dependencies
    print_step(4, "Installing Backend Dependencies")
    if not install_backend_dependencies():
        print("❌ Backend setup failed. Please install dependencies manually.")
        return
    
    # Check frontend
    print_step(5, "Checking Frontend Dependencies")
    check_frontend_dependencies()
    
    # Final instructions
    print_header("Setup Complete! 🎉")
    
    print("\n🚀 How to start the system:")
    print("\n1. Start Backend Server:")
    print("   cd backend")
    if os.name == 'nt':
        print("   venv\\Scripts\\python.exe run.py")
    else:
        print("   venv/bin/python run.py")
    print("   (Backend will run on http://127.0.0.1:8000)")
    
    print("\n2. Start Frontend (in a new terminal):")
    print("   npm run dev")
    print("   (Frontend will run on http://localhost:5173)")
    
    print("\n👥 Team Member Login Details:")
    print("   • team_member_1 (Team Member 1)")
    print("   • team_member_2 (Team Member 2)")
    print("   • team_member_3 (Team Member 3)")
    
    print("\n🔐 Authentication Process for Each Team Member:")
    print("   1. Go to http://localhost:5173")
    print("   2. Click 'Show authorized team members' to see usernames")
    print("   3. Enter your username and click 'Proceed to Verification'")
    print("   4. Complete Face Enrollment (capture your face)")
    print("   5. Complete Voice Enrollment (record your voice)")
    print("   6. Test authentication with face and voice verification")
    
    print("\n📊 Monitor System:")
    print("   • Backend API docs: http://127.0.0.1:8000/docs")
    print("   • Health check: http://127.0.0.1:8000/health")
    print("   • User status: http://127.0.0.1:8000/api/auth/authorized-users")
    
    print("\n✨ System is ready for your team!")

if __name__ == "__main__":
    main()
