/**
 * Custom hook for managing camera and microphone capture
 */

import { useState, useRef, useCallback, useEffect } from 'react';

export interface MediaCaptureState {
  isVideoReady: boolean;
  isRecording: boolean;
  error: string | null;
  hasPermissions: boolean;
}

export interface MediaCaptureActions {
  startVideo: () => Promise<void>;
  stopVideo: () => void;
  captureImage: () => Promise<Blob | null>;
  startRecording: () => Promise<void>;
  stopRecording: () => Promise<Blob | null>;
  requestPermissions: () => Promise<boolean>;
}

export const useMediaCapture = () => {
  const [state, setState] = useState<MediaCaptureState>({
    isVideoReady: false,
    isRecording: false,
    error: null,
    hasPermissions: false,
  });

  const videoRef = useRef<HTMLVideoElement>(null);
  const streamRef = useRef<MediaStream | null>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);

  // Request camera and microphone permissions
  const requestPermissions = useCallback(async (): Promise<boolean> => {
    try {
      console.log('🔐 Requesting camera and microphone permissions...');
      setState(prev => ({ ...prev, error: null }));

      // Check if getUserMedia is available
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        throw new Error('Camera/microphone access not supported in this browser');
      }

      const stream = await navigator.mediaDevices.getUserMedia({
        video: true,
        audio: true,
      });

      console.log('✅ Permissions granted');

      // Stop the stream immediately - we just wanted to check permissions
      stream.getTracks().forEach(track => {
        console.log(`🛑 Stopping ${track.kind} track`);
        track.stop();
      });

      setState(prev => ({ ...prev, hasPermissions: true }));
      return true;
    } catch (error) {
      console.error('❌ Permission request failed:', error);
      const errorMessage = error instanceof Error ? error.message : 'Permission denied';
      let userFriendlyMessage = `Camera/microphone access denied: ${errorMessage}`;

      // Provide more specific error messages
      if (errorMessage.includes('NotAllowedError') || errorMessage.includes('Permission denied')) {
        userFriendlyMessage = 'Please allow camera and microphone access in your browser settings and try again.';
      } else if (errorMessage.includes('NotFoundError')) {
        userFriendlyMessage = 'No camera or microphone found. Please connect a camera and microphone.';
      } else if (errorMessage.includes('NotSupportedError')) {
        userFriendlyMessage = 'Camera/microphone access not supported in this browser. Try using Chrome or Firefox.';
      }

      setState(prev => ({
        ...prev,
        error: userFriendlyMessage,
        hasPermissions: false
      }));
      return false;
    }
  }, []);

  // Start video stream
  const startVideo = useCallback(async (): Promise<void> => {
    try {
      console.log('🎥 Starting video stream...');
      setState(prev => ({ ...prev, error: null }));

      // First check if video element is available
      if (!videoRef.current) {
        console.error('❌ Video element not available');
        throw new Error('Video element not available. Please ensure the camera component is rendered.');
      }

      if (streamRef.current) {
        console.log('🔄 Stopping existing stream...');
        stopVideo();
      }

      console.log('📹 Requesting camera access...');
      const stream = await navigator.mediaDevices.getUserMedia({
        video: {
          width: { ideal: 640 },
          height: { ideal: 480 },
          facingMode: 'user',
        },
        audio: false, // We'll handle audio separately
      });

      console.log('✅ Camera stream obtained');
      streamRef.current = stream;

      // Double-check video element is still available
      if (!videoRef.current) {
        console.error('❌ Video element became unavailable during stream setup');
        throw new Error('Video element became unavailable during setup.');
      }

      console.log('🔗 Connecting stream to video element...');
      videoRef.current.srcObject = stream;

      // Add multiple event listeners for better debugging
      videoRef.current.onloadedmetadata = () => {
        console.log('✅ Video metadata loaded, camera ready');
        setState(prev => ({ ...prev, isVideoReady: true }));
      };

      videoRef.current.oncanplay = () => {
        console.log('✅ Video can play');
      };

      videoRef.current.onerror = (e) => {
        console.error('❌ Video element error:', e);
        setState(prev => ({
          ...prev,
          error: 'Video playback error',
          isVideoReady: false
        }));
      };

      // Force play the video
      try {
        await videoRef.current.play();
        console.log('✅ Video playback started');
      } catch (playError) {
        console.warn('⚠️ Video autoplay failed (this is normal):', playError);
        // This is often normal due to browser autoplay policies
      }
    } catch (error) {
      console.error('❌ Camera start error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to start video';
      setState(prev => ({
        ...prev,
        error: `Failed to start camera: ${errorMessage}`,
        isVideoReady: false
      }));

      // Clean up stream if we got one but failed to connect it
      if (streamRef.current) {
        streamRef.current.getTracks().forEach(track => track.stop());
        streamRef.current = null;
      }
    }
  }, []);

  // Stop video stream
  const stopVideo = useCallback((): void => {
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }

    if (videoRef.current) {
      videoRef.current.srcObject = null;
    }

    setState(prev => ({ ...prev, isVideoReady: false }));
  }, []);

  // Capture image from video
  const captureImage = useCallback(async (): Promise<Blob | null> => {
    if (!videoRef.current || !state.isVideoReady) {
      setState(prev => ({ ...prev, error: 'Video not ready for capture' }));
      return null;
    }

    try {
      const video = videoRef.current;
      const canvas = document.createElement('canvas');
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;

      const ctx = canvas.getContext('2d');
      if (!ctx) {
        throw new Error('Failed to get canvas context');
      }

      ctx.drawImage(video, 0, 0);

      return new Promise((resolve) => {
        canvas.toBlob((blob) => {
          resolve(blob);
        }, 'image/jpeg', 0.8);
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to capture image';
      setState(prev => ({ ...prev, error: `Image capture failed: ${errorMessage}` }));
      return null;
    }
  }, [state.isVideoReady]);

  // Start audio recording
  const startRecording = useCallback(async (): Promise<void> => {
    try {
      setState(prev => ({ ...prev, error: null }));

      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          sampleRate: 44100,
        },
        video: false,
      });

      const mediaRecorder = new MediaRecorder(stream, {
        mimeType: 'audio/webm;codecs=opus',
      });

      audioChunksRef.current = [];

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };

      mediaRecorder.onstop = () => {
        stream.getTracks().forEach(track => track.stop());
      };

      mediaRecorderRef.current = mediaRecorder;
      mediaRecorder.start();

      setState(prev => ({ ...prev, isRecording: true }));
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to start recording';
      setState(prev => ({ 
        ...prev, 
        error: `Recording failed: ${errorMessage}`,
        isRecording: false 
      }));
    }
  }, []);

  // Stop audio recording
  const stopRecording = useCallback(async (): Promise<Blob | null> => {
    return new Promise((resolve) => {
      if (!mediaRecorderRef.current || !state.isRecording) {
        setState(prev => ({ ...prev, error: 'No active recording to stop' }));
        resolve(null);
        return;
      }

      mediaRecorderRef.current.onstop = () => {
        const audioBlob = new Blob(audioChunksRef.current, { 
          type: 'audio/webm;codecs=opus' 
        });
        audioChunksRef.current = [];
        setState(prev => ({ ...prev, isRecording: false }));
        resolve(audioBlob);
      };

      mediaRecorderRef.current.stop();
    });
  }, [state.isRecording]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopVideo();
      if (mediaRecorderRef.current && state.isRecording) {
        mediaRecorderRef.current.stop();
      }
    };
  }, [stopVideo, state.isRecording]);

  // Clear error after 5 seconds
  useEffect(() => {
    if (state.error) {
      const timer = setTimeout(() => {
        setState(prev => ({ ...prev, error: null }));
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [state.error]);

  return {
    // State
    ...state,
    
    // Refs
    videoRef,
    
    // Actions
    startVideo,
    stopVideo,
    captureImage,
    startRecording,
    stopRecording,
    requestPermissions,
  };
};
