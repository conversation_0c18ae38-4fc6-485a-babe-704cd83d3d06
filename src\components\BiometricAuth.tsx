
import { useState, useRef, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Camera, Mic, MicOff, ArrowLeft, CheckCircle, XCircle } from 'lucide-react';

interface BiometricAuthProps {
  userName: string;
  onSuccess: () => void;
  onBack: () => void;
}

type AuthStatus = 'idle' | 'camera' | 'audio' | 'processing' | 'success' | 'failed';

const BiometricAuth = ({ userName, onSuccess, onBack }: BiometricAuthProps) => {
  const [authStatus, setAuthStatus] = useState<AuthStatus>('idle');
  const [isRecording, setIsRecording] = useState(false);
  const [faceDetected, setFaceDetected] = useState(false);
  const [voiceDetected, setVoiceDetected] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);
  const streamRef = useRef<MediaStream | null>(null);

  const startCamera = async () => {
    try {
      setAuthStatus('camera');
      const stream = await navigator.mediaDevices.getUserMedia({ 
        video: { width: 640, height: 480 }, 
        audio: false 
      });
      streamRef.current = stream;
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
      }
      
      // Simulate face detection after 3 seconds
      setTimeout(() => {
        setFaceDetected(true);
        setTimeout(() => startAudioRecording(), 1000);
      }, 3000);
    } catch (error) {
      console.error('Error accessing camera:', error);
      setAuthStatus('failed');
    }
  };

  const startAudioRecording = async () => {
    try {
      setAuthStatus('audio');
      setIsRecording(true);
      
      // Simulate audio recording and voice detection
      setTimeout(() => {
        setVoiceDetected(true);
        setIsRecording(false);
        processAuthentication();
      }, 4000);
    } catch (error) {
      console.error('Error accessing microphone:', error);
      setAuthStatus('failed');
    }
  };

  const processAuthentication = () => {
    setAuthStatus('processing');
    
    // Simulate processing time
    setTimeout(() => {
      // Simulate successful authentication (90% success rate)
      const success = Math.random() > 0.1;
      if (success) {
        setAuthStatus('success');
        setTimeout(() => {
          onSuccess();
        }, 2000);
      } else {
        setAuthStatus('failed');
      }
    }, 3000);
  };

  const stopCamera = () => {
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }
  };

  useEffect(() => {
    return () => {
      stopCamera();
    };
  }, []);

  const getStatusMessage = () => {
    switch (authStatus) {
      case 'idle':
        return 'Ready to begin biometric verification';
      case 'camera':
        return faceDetected ? 'Face detected! Proceeding...' : 'Look directly at the camera';
      case 'audio':
        return 'Please speak clearly for 3 seconds';
      case 'processing':
        return 'Processing biometric data...';
      case 'success':
        return 'Authentication successful!';
      case 'failed':
        return 'Authentication failed. Please try again.';
      default:
        return '';
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
        className="w-full max-w-4xl"
      >
        <Card className="bg-white/10 backdrop-blur-lg border-white/20 shadow-2xl">
          <CardHeader className="text-center">
            <Button
              variant="ghost"
              onClick={onBack}
              className="absolute top-4 left-4 text-white hover:bg-white/10"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back
            </Button>
            <CardTitle className="text-2xl font-bold text-white">
              Biometric Authentication
            </CardTitle>
            <CardDescription className="text-gray-300">
              Welcome, {userName}. Please complete face and voice verification.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Camera Section */}
            <div className="flex flex-col lg:flex-row gap-6">
              <div className="flex-1">
                <div className="relative bg-black rounded-lg overflow-hidden aspect-video">
                  {authStatus !== 'idle' ? (
                    <video
                      ref={videoRef}
                      autoPlay
                      muted
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center">
                      <Camera className="w-16 h-16 text-gray-400" />
                    </div>
                  )}
                  
                  {/* Face detection overlay */}
                  {authStatus === 'camera' && (
                    <div className="absolute inset-0 flex items-center justify-center">
                      <motion.div
                        animate={{
                          scale: faceDetected ? 1 : [1, 1.1, 1],
                          borderColor: faceDetected ? '#10B981' : '#3B82F6'
                        }}
                        transition={{
                          scale: { repeat: faceDetected ? 0 : Infinity, duration: 1.5 },
                          borderColor: { duration: 0.3 }
                        }}
                        className="w-48 h-48 border-4 rounded-full"
                      />
                      {faceDetected && (
                        <motion.div
                          initial={{ scale: 0 }}
                          animate={{ scale: 1 }}
                          className="absolute"
                        >
                          <CheckCircle className="w-8 h-8 text-green-400" />
                        </motion.div>
                      )}
                    </div>
                  )}
                </div>
              </div>

              {/* Status Panel */}
              <div className="flex-1 space-y-4">
                <div className="p-6 bg-white/5 rounded-lg border border-white/10">
                  <h3 className="text-lg font-semibold text-white mb-4">Verification Status</h3>
                  
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-gray-300">Face Recognition</span>
                      {faceDetected ? (
                        <CheckCircle className="w-5 h-5 text-green-400" />
                      ) : (
                        <div className="w-5 h-5 border-2 border-gray-400 rounded-full animate-spin border-t-blue-400" />
                      )}
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <span className="text-gray-300">Voice Recognition</span>
                      {voiceDetected ? (
                        <CheckCircle className="w-5 h-5 text-green-400" />
                      ) : authStatus === 'audio' ? (
                        <div className="w-5 h-5 border-2 border-gray-400 rounded-full animate-spin border-t-blue-400" />
                      ) : (
                        <div className="w-5 h-5 border border-gray-400 rounded-full" />
                      )}
                    </div>
                  </div>

                  {/* Audio Recording Indicator */}
                  {authStatus === 'audio' && (
                    <motion.div
                      animate={{ scale: isRecording ? [1, 1.2, 1] : 1 }}
                      transition={{ repeat: isRecording ? Infinity : 0, duration: 1 }}
                      className="mt-4 flex items-center justify-center"
                    >
                      {isRecording ? (
                        <Mic className="w-8 h-8 text-red-400" />
                      ) : (
                        <MicOff className="w-8 h-8 text-gray-400" />
                      )}
                    </motion.div>
                  )}
                </div>

                {/* Status Message */}
                <div className="text-center">
                  <motion.p
                    key={authStatus}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="text-white text-lg"
                  >
                    {getStatusMessage()}
                  </motion.p>
                </div>

                {/* Action Buttons */}
                <div className="flex gap-3">
                  {authStatus === 'idle' && (
                    <Button
                      onClick={startCamera}
                      className="flex-1 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
                    >
                      <Camera className="w-4 h-4 mr-2" />
                      Start Verification
                    </Button>
                  )}
                  
                  {authStatus === 'failed' && (
                    <Button
                      onClick={() => {
                        setAuthStatus('idle');
                        setFaceDetected(false);
                        setVoiceDetected(false);
                        stopCamera();
                      }}
                      className="flex-1 bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800"
                    >
                      <XCircle className="w-4 h-4 mr-2" />
                      Try Again
                    </Button>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
};

export default BiometricAuth;
