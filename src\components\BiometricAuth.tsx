
import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Camera, Mic, MicOff, ArrowLeft, CheckCircle, XCircle, Loader2, AlertCircle } from 'lucide-react';
import { useMediaCapture } from '@/hooks/useMediaCapture';
import { apiService } from '@/services/api';

interface BiometricAuthProps {
  userName: string;
  onSuccess: () => void;
  onBack: () => void;
}

type AuthStatus = 'idle' | 'camera' | 'audio' | 'processing' | 'success' | 'failed';

interface AuthResults {
  faceResult?: { success: boolean; confidence?: number; message: string };
  voiceResult?: { success: boolean; confidence?: number; message: string };
  error?: string;
}

const BiometricAuth = ({ userName, onSuccess, onBack }: BiometricAuthProps) => {
  const [authStatus, setAuthStatus] = useState<AuthStatus>('idle');
  const [authResults, setAuthResults] = useState<AuthResults>({});
  const [isLoading, setIsLoading] = useState(false);
  const [currentStep, setCurrentStep] = useState<'face' | 'voice' | 'complete'>('face');

  const {
    isVideoReady,
    isRecording,
    error: mediaError,
    hasPermissions,
    videoRef,
    startVideo,
    stopVideo,
    captureImage,
    startRecording,
    stopRecording,
    requestPermissions,
  } = useMediaCapture();

  // Initialize permissions on component mount
  useEffect(() => {
    requestPermissions();
  }, [requestPermissions]);

  // Start the authentication process
  const startAuthentication = async () => {
    if (!hasPermissions) {
      const granted = await requestPermissions();
      if (!granted) return;
    }

    setAuthStatus('camera');
    setCurrentStep('face');
    setAuthResults({});
    await startVideo();
  };

  // Capture and verify face
  const handleFaceCapture = async () => {
    if (!isVideoReady) return;

    setIsLoading(true);
    try {
      const imageBlob = await captureImage();
      if (!imageBlob) {
        throw new Error('Failed to capture image');
      }

      // Convert blob to base64 for API
      const base64Image = await new Promise<string>((resolve) => {
        const reader = new FileReader();
        reader.onloadend = () => {
          const result = reader.result as string;
          const base64 = result.split(',')[1];
          resolve(base64);
        };
        reader.readAsDataURL(imageBlob);
      });

      // Call face verification API
      const result = await apiService.verifyFace(userName, base64Image);

      setAuthResults(prev => ({
        ...prev,
        faceResult: {
          success: result.success,
          confidence: result.confidence,
          message: result.message
        }
      }));

      if (result.success) {
        // Move to voice verification
        setCurrentStep('voice');
        setAuthStatus('audio');
        stopVideo();
      } else {
        setAuthStatus('failed');
      }
    } catch (error) {
      console.error('Face verification error:', error);
      setAuthResults(prev => ({
        ...prev,
        faceResult: {
          success: false,
          message: error instanceof Error ? error.message : 'Face verification failed'
        },
        error: 'Face verification failed'
      }));
      setAuthStatus('failed');
    } finally {
      setIsLoading(false);
    }
  };

  // Start voice recording
  const handleVoiceStart = async () => {
    setIsLoading(true);
    try {
      await startRecording();
    } catch (error) {
      console.error('Voice recording error:', error);
      setAuthResults(prev => ({
        ...prev,
        error: 'Failed to start voice recording'
      }));
      setAuthStatus('failed');
    } finally {
      setIsLoading(false);
    }
  };

  // Stop voice recording and verify
  const handleVoiceStop = async () => {
    setIsLoading(true);
    try {
      const audioBlob = await stopRecording();
      if (!audioBlob) {
        throw new Error('Failed to capture audio');
      }

      // Call voice verification API
      const result = await apiService.verifyVoice(userName, audioBlob);

      setAuthResults(prev => ({
        ...prev,
        voiceResult: {
          success: result.success,
          confidence: result.confidence,
          message: result.message
        }
      }));

      if (result.success) {
        setCurrentStep('complete');
        setAuthStatus('processing');

        // Check overall authentication success
        const faceSuccess = authResults.faceResult?.success ?? false;
        const voiceSuccess = result.success;

        if (faceSuccess && voiceSuccess) {
          setTimeout(() => {
            setAuthStatus('success');
            setTimeout(() => onSuccess(), 2000);
          }, 1500);
        } else {
          setAuthStatus('failed');
        }
      } else {
        setAuthStatus('failed');
      }
    } catch (error) {
      console.error('Voice verification error:', error);
      setAuthResults(prev => ({
        ...prev,
        voiceResult: {
          success: false,
          message: error instanceof Error ? error.message : 'Voice verification failed'
        },
        error: 'Voice verification failed'
      }));
      setAuthStatus('failed');
    } finally {
      setIsLoading(false);
    }
  };

  // Reset authentication
  const resetAuthentication = () => {
    setAuthStatus('idle');
    setCurrentStep('face');
    setAuthResults({});
    setIsLoading(false);
    stopVideo();
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopVideo();
    };
  }, [stopVideo]);

  const getStatusMessage = () => {
    if (mediaError) return mediaError;
    if (authResults.error) return authResults.error;

    switch (authStatus) {
      case 'idle':
        return hasPermissions
          ? 'Ready to begin biometric verification'
          : 'Please grant camera and microphone permissions';
      case 'camera':
        return currentStep === 'face'
          ? 'Position your face in the camera and click Capture Face'
          : 'Face verification complete';
      case 'audio':
        return isRecording
          ? 'Recording... Please speak clearly'
          : 'Click Start Recording and speak for a few seconds';
      case 'processing':
        return 'Processing biometric data...';
      case 'success':
        return 'Authentication successful!';
      case 'failed':
        return 'Authentication failed. Please try again.';
      default:
        return '';
    }
  };

  const getConfidenceDisplay = (result?: { success: boolean; confidence?: number }) => {
    if (!result) return null;
    if (result.confidence === undefined) return null;

    const percentage = Math.round(result.confidence * 100);
    const color = percentage >= 70 ? 'text-green-400' : percentage >= 50 ? 'text-yellow-400' : 'text-red-400';

    return (
      <span className={`text-sm ${color}`}>
        ({percentage}% confidence)
      </span>
    );
  };

  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
        className="w-full max-w-4xl"
      >
        <Card className="bg-white/10 backdrop-blur-lg border-white/20 shadow-2xl">
          <CardHeader className="text-center">
            <Button
              variant="ghost"
              onClick={onBack}
              className="absolute top-4 left-4 text-white hover:bg-white/10"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back
            </Button>
            <CardTitle className="text-2xl font-bold text-white">
              Biometric Authentication
            </CardTitle>
            <CardDescription className="text-gray-300">
              Welcome, {userName}. Please complete face and voice verification.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Camera Section */}
            <div className="flex flex-col lg:flex-row gap-6">
              <div className="flex-1">
                <div className="relative bg-black rounded-lg overflow-hidden aspect-video">
                  {(authStatus === 'camera' && isVideoReady) ? (
                    <video
                      ref={videoRef}
                      autoPlay
                      muted
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center">
                      {authStatus === 'audio' ? (
                        <Mic className="w-16 h-16 text-blue-400" />
                      ) : (
                        <Camera className="w-16 h-16 text-gray-400" />
                      )}
                    </div>
                  )}

                  {/* Face capture overlay */}
                  {authStatus === 'camera' && currentStep === 'face' && isVideoReady && (
                    <div className="absolute inset-0 flex items-center justify-center">
                      <motion.div
                        animate={{
                          scale: [1, 1.05, 1],
                          borderColor: '#3B82F6'
                        }}
                        transition={{
                          scale: { repeat: Infinity, duration: 2 },
                          borderColor: { duration: 0.3 }
                        }}
                        className="w-48 h-48 border-4 rounded-full"
                      />
                    </div>
                  )}

                  {/* Audio recording indicator */}
                  {authStatus === 'audio' && (
                    <div className="absolute inset-0 flex items-center justify-center">
                      <motion.div
                        animate={{
                          scale: isRecording ? [1, 1.2, 1] : 1,
                          opacity: isRecording ? [1, 0.7, 1] : 1
                        }}
                        transition={{
                          repeat: isRecording ? Infinity : 0,
                          duration: 1
                        }}
                        className={`w-24 h-24 rounded-full flex items-center justify-center ${
                          isRecording ? 'bg-red-500/20' : 'bg-blue-500/20'
                        }`}
                      >
                        {isRecording ? (
                          <Mic className="w-12 h-12 text-red-400" />
                        ) : (
                          <MicOff className="w-12 h-12 text-gray-400" />
                        )}
                      </motion.div>
                    </div>
                  )}

                  {/* Loading overlay */}
                  {isLoading && (
                    <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
                      <Loader2 className="w-8 h-8 text-white animate-spin" />
                    </div>
                  )}
                </div>
              </div>

              {/* Status Panel */}
              <div className="flex-1 space-y-4">
                <div className="p-6 bg-white/5 rounded-lg border border-white/10">
                  <h3 className="text-lg font-semibold text-white mb-4">Verification Status</h3>

                  <div className="space-y-4">
                    {/* Face Recognition Status */}
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-gray-300">Face Recognition</span>
                        {authResults.faceResult?.success ? (
                          <CheckCircle className="w-5 h-5 text-green-400" />
                        ) : authResults.faceResult?.success === false ? (
                          <XCircle className="w-5 h-5 text-red-400" />
                        ) : currentStep === 'face' && authStatus === 'camera' ? (
                          <div className="w-5 h-5 border-2 border-gray-400 rounded-full animate-spin border-t-blue-400" />
                        ) : (
                          <div className="w-5 h-5 border border-gray-400 rounded-full" />
                        )}
                      </div>
                      {authResults.faceResult && (
                        <div className="text-sm text-gray-400 flex items-center gap-2">
                          <span>{authResults.faceResult.message}</span>
                          {getConfidenceDisplay(authResults.faceResult)}
                        </div>
                      )}
                    </div>

                    {/* Voice Recognition Status */}
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-gray-300">Voice Recognition</span>
                        {authResults.voiceResult?.success ? (
                          <CheckCircle className="w-5 h-5 text-green-400" />
                        ) : authResults.voiceResult?.success === false ? (
                          <XCircle className="w-5 h-5 text-red-400" />
                        ) : currentStep === 'voice' && (authStatus === 'audio' || isRecording) ? (
                          <div className="w-5 h-5 border-2 border-gray-400 rounded-full animate-spin border-t-blue-400" />
                        ) : (
                          <div className="w-5 h-5 border border-gray-400 rounded-full" />
                        )}
                      </div>
                      {authResults.voiceResult && (
                        <div className="text-sm text-gray-400 flex items-center gap-2">
                          <span>{authResults.voiceResult.message}</span>
                          {getConfidenceDisplay(authResults.voiceResult)}
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Error Display */}
                  {(mediaError || authResults.error) && (
                    <div className="mt-4 p-3 bg-red-500/10 border border-red-500/20 rounded-lg">
                      <div className="flex items-center gap-2 text-red-400">
                        <AlertCircle className="w-4 h-4" />
                        <span className="text-sm">{mediaError || authResults.error}</span>
                      </div>
                    </div>
                  )}
                </div>

                {/* Status Message */}
                <div className="text-center">
                  <motion.p
                    key={authStatus}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="text-white text-lg"
                  >
                    {getStatusMessage()}
                  </motion.p>
                </div>

                {/* Action Buttons */}
                <div className="flex gap-3">
                  {authStatus === 'idle' && (
                    <Button
                      onClick={startAuthentication}
                      disabled={!hasPermissions || isLoading}
                      className="flex-1 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 disabled:opacity-50"
                    >
                      {isLoading ? (
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      ) : (
                        <Camera className="w-4 h-4 mr-2" />
                      )}
                      Start Verification
                    </Button>
                  )}

                  {authStatus === 'camera' && currentStep === 'face' && isVideoReady && (
                    <Button
                      onClick={handleFaceCapture}
                      disabled={isLoading}
                      className="flex-1 bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 disabled:opacity-50"
                    >
                      {isLoading ? (
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      ) : (
                        <Camera className="w-4 h-4 mr-2" />
                      )}
                      Capture Face
                    </Button>
                  )}

                  {authStatus === 'audio' && currentStep === 'voice' && (
                    <>
                      {!isRecording ? (
                        <Button
                          onClick={handleVoiceStart}
                          disabled={isLoading}
                          className="flex-1 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 disabled:opacity-50"
                        >
                          {isLoading ? (
                            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                          ) : (
                            <Mic className="w-4 h-4 mr-2" />
                          )}
                          Start Recording
                        </Button>
                      ) : (
                        <Button
                          onClick={handleVoiceStop}
                          disabled={isLoading}
                          className="flex-1 bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 disabled:opacity-50"
                        >
                          {isLoading ? (
                            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                          ) : (
                            <MicOff className="w-4 h-4 mr-2" />
                          )}
                          Stop Recording
                        </Button>
                      )}
                    </>
                  )}

                  {authStatus === 'failed' && (
                    <Button
                      onClick={resetAuthentication}
                      className="flex-1 bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800"
                    >
                      <XCircle className="w-4 h-4 mr-2" />
                      Try Again
                    </Button>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
};

export default BiometricAuth;
