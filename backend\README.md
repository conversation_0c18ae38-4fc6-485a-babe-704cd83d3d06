# Persona Audio Gatekeeper Backend

A Python FastAPI backend for face and audio recognition authentication that syncs with the React frontend.

## Features

- 🎭 **Face Recognition**: Real-time face detection and verification using OpenCV and face_recognition
- 🎤 **Voice Recognition**: Audio processing and voice verification using speech recognition
- 🔐 **Biometric Authentication**: Combined face and voice authentication
- 📊 **User Management**: User registration, enrollment, and authentication logging
- 🌐 **REST API**: FastAPI-based REST API with automatic documentation
- 🗄️ **Database**: SQLite database for user data and authentication logs
- 🔒 **Security**: CORS configuration and secure authentication endpoints

## Requirements

- Python 3.8 or higher
- Webcam (for face recognition)
- Microphone (for voice recognition)

## Quick Start

### 1. Automatic Setup (Recommended)

```bash
cd backend
python setup.py
```

This will:
- Create a virtual environment
- Install all dependencies
- Set up configuration files

### 2. Manual Setup

```bash
cd backend

# Create virtual environment
python -m venv venv

# Activate virtual environment
# On Windows:
venv\Scripts\activate
# On macOS/Linux:
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# Copy environment configuration
cp .env.example .env
```

### 3. Run the Server

```bash
# Using the run script (recommended)
python run.py

# Or directly with uvicorn
uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```

The server will be available at:
- **API**: http://localhost:8000
- **Documentation**: http://localhost:8000/docs
- **Alternative docs**: http://localhost:8000/redoc

## API Endpoints

### Authentication
- `POST /api/auth/register` - Register a new user
- `GET /api/auth/user/{username}` - Get user information
- `GET /api/auth/users` - List all users
- `GET /api/auth/logs/{username}` - Get authentication logs

### Face Recognition
- `POST /api/face/enroll` - Enroll user's face
- `POST /api/face/verify` - Verify user's face
- `POST /api/face/detect` - Detect faces in image

### Voice Recognition
- `POST /api/audio/enroll` - Enroll user's voice
- `POST /api/audio/verify` - Verify user's voice
- `POST /api/audio/analyze` - Analyze audio file

### Biometric Authentication
- `POST /api/biometric/authenticate` - Combined face and voice authentication
- `GET /api/biometric/status/{username}` - Get biometric enrollment status

## Usage Examples

### 1. Register a User

```bash
curl -X POST "http://localhost:8000/api/auth/register" \
  -H "Content-Type: application/json" \
  -d '{"username": "john_doe", "email": "<EMAIL>"}'
```

### 2. Enroll Face

```bash
curl -X POST "http://localhost:8000/api/face/enroll" \
  -F "username=john_doe" \
  -F "image=@face_photo.jpg"
```

### 3. Enroll Voice

```bash
curl -X POST "http://localhost:8000/api/audio/enroll" \
  -F "username=john_doe" \
  -F "audio=@voice_sample.wav"
```

### 4. Authenticate

```bash
curl -X POST "http://localhost:8000/api/biometric/authenticate" \
  -F "username=john_doe" \
  -F "face_image=@current_face.jpg" \
  -F "voice_audio=@current_voice.wav"
```

## Configuration

Edit the `.env` file to configure the backend:

```env
# Database
DATABASE_URL=sqlite:///./biometric_auth.db

# Server
API_HOST=0.0.0.0
API_PORT=8000
DEBUG=True

# Recognition Thresholds
FACE_RECOGNITION_THRESHOLD=0.6
VOICE_RECOGNITION_THRESHOLD=0.6

# CORS
ALLOWED_ORIGINS=http://localhost:5173,http://localhost:3000
```

## Frontend Integration

The backend is designed to work with the React frontend. The frontend should:

1. Register users via `/api/auth/register`
2. Enroll biometric data via `/api/face/enroll` and `/api/audio/enroll`
3. Authenticate users via `/api/biometric/authenticate`

## Database Schema

### Users Table
- `id`: Primary key
- `username`: Unique username
- `email`: User email
- `face_encoding`: JSON-encoded face features
- `voice_print`: JSON-encoded voice features
- `is_active`: User status
- `created_at`, `updated_at`: Timestamps

### Authentication Logs Table
- `id`: Primary key
- `user_id`: Reference to user
- `username`: Username for quick lookup
- `auth_type`: Type of authentication (face, voice, combined)
- `success`: Authentication result
- `confidence_score`: Confidence level
- `ip_address`, `user_agent`: Request metadata
- `timestamp`: When authentication occurred

## Troubleshooting

### Common Issues

1. **Import errors**: Make sure you've activated the virtual environment
2. **Camera not working**: Check camera permissions and ensure no other app is using it
3. **Audio not working**: Check microphone permissions
4. **Face recognition fails**: Ensure good lighting and face is clearly visible
5. **Voice recognition fails**: Speak clearly and ensure minimal background noise

### Dependencies Issues

If you encounter issues with specific dependencies:

```bash
# For face_recognition on Windows, you might need:
pip install cmake
pip install dlib
pip install face_recognition

# For audio processing:
pip install pyaudio
# On Ubuntu/Debian:
sudo apt-get install portaudio19-dev python3-pyaudio
```

## Development

### Project Structure

```
backend/
├── app/
│   ├── routers/          # API route handlers
│   ├── models.py         # Database models
│   ├── schemas.py        # Pydantic schemas
│   └── database.py       # Database configuration
├── main.py              # FastAPI application
├── run.py               # Server runner script
├── setup.py             # Setup script
├── requirements.txt     # Python dependencies
└── README.md           # This file
```

### Adding New Features

1. Create new router in `app/routers/`
2. Add database models in `app/models.py`
3. Define schemas in `app/schemas.py`
4. Register router in `main.py`

## Security Considerations

- Face and voice data are stored as encoded features, not raw biometric data
- All authentication attempts are logged
- CORS is configured for specific origins
- Consider adding rate limiting for production use
- Use HTTPS in production

## License

This project is part of the Persona Audio Gatekeeper system.
