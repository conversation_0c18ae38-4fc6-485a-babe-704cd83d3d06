from pydantic import BaseModel
from typing import Optional

class FaceRecognitionResponse(BaseModel):
    success: bool
    message: str
    confidence: Optional[float] = None
    user_id: Optional[int] = None

class EnrollmentResponse(BaseModel):
    success: bool
    message: str
    user_id: Optional[int] = None

class AudioVerificationResponse(BaseModel):
    success: bool
    message: str
    confidence: Optional[float] = None
    user_id: Optional[int] = None

class AudioRecognitionResponse(BaseModel):
    success: bool
    message: str
    confidence: Optional[float] = None
    user_id: Optional[int] = None
