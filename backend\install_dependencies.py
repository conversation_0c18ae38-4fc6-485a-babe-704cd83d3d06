#!/usr/bin/env python3
"""
Specialized dependency installer for Persona Audio Gatekeeper Backend
Handles problematic packages like face_recognition, dlib, and pyaudio
"""

import subprocess
import sys
import os
import platform
import importlib.util

def run_command(command, description, ignore_errors=False):
    """Run a command and handle errors"""
    print(f"🔧 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        if result.stdout.strip():
            print(f"   Output: {result.stdout.strip()}")
        return True
    except subprocess.CalledProcessError as e:
        if ignore_errors:
            print(f"⚠️  {description} failed (continuing anyway)")
            print(f"   Error: {e.stderr.strip()}")
            return False
        else:
            print(f"❌ {description} failed:")
            print(f"   Error: {e.stderr.strip()}")
            return False

def check_package_installed(package_name):
    """Check if a package is installed"""
    spec = importlib.util.find_spec(package_name)
    return spec is not None

def install_basic_packages():
    """Install basic packages first"""
    basic_packages = [
        "wheel",
        "setuptools",
        "pip --upgrade",
        "numpy==1.24.3",
        "pillow==10.0.1",
    ]
    
    for package in basic_packages:
        if not run_command(f"pip install {package}", f"Installing {package}"):
            return False
    return True

def install_cmake():
    """Install CMake (required for dlib)"""
    system = platform.system().lower()
    
    if system == "windows":
        # Try to install cmake via pip first
        if run_command("pip install cmake", "Installing CMake via pip", ignore_errors=True):
            return True
        
        print("⚠️  CMake installation via pip failed.")
        print("📋 Please install CMake manually:")
        print("   1. Download from: https://cmake.org/download/")
        print("   2. Install and add to PATH")
        print("   3. Restart your terminal/command prompt")
        print("   4. Run this script again")
        return False
    
    elif system == "darwin":  # macOS
        if run_command("brew install cmake", "Installing CMake via Homebrew", ignore_errors=True):
            return True
        print("⚠️  Please install CMake: brew install cmake")
        return False
    
    else:  # Linux
        if run_command("sudo apt-get update && sudo apt-get install -y cmake", "Installing CMake via apt", ignore_errors=True):
            return True
        print("⚠️  Please install CMake: sudo apt-get install cmake")
        return False

def install_dlib():
    """Install dlib with proper configuration"""
    system = platform.system().lower()
    
    # Check if dlib is already installed
    if check_package_installed("dlib"):
        print("✅ dlib is already installed")
        return True
    
    print("🔧 Installing dlib (this may take several minutes)...")
    
    if system == "windows":
        # Try precompiled wheel first
        if run_command("pip install dlib", "Installing dlib precompiled wheel", ignore_errors=True):
            return True
        
        # If that fails, try building from source
        print("⚠️  Precompiled dlib failed, trying to build from source...")
        if run_command("pip install dlib --no-cache-dir", "Building dlib from source", ignore_errors=True):
            return True
        
        print("❌ dlib installation failed. Trying alternative approach...")
        print("📋 Manual installation steps:")
        print("   1. Install Visual Studio Build Tools")
        print("   2. Or try: conda install -c conda-forge dlib")
        return False
    
    else:
        # Linux/macOS
        return run_command("pip install dlib", "Installing dlib")

def install_face_recognition():
    """Install face_recognition package"""
    if check_package_installed("face_recognition"):
        print("✅ face_recognition is already installed")
        return True
    
    return run_command("pip install face_recognition", "Installing face_recognition")

def install_opencv():
    """Install OpenCV"""
    if check_package_installed("cv2"):
        print("✅ OpenCV is already installed")
        return True
    
    return run_command("pip install opencv-python==********", "Installing OpenCV")

def install_audio_packages():
    """Install audio processing packages"""
    system = platform.system().lower()
    
    # Install pydub first (easier)
    if not run_command("pip install pydub==0.25.1", "Installing pydub"):
        return False
    
    # Install SpeechRecognition
    if not run_command("pip install speechrecognition==3.10.0", "Installing SpeechRecognition"):
        return False
    
    # Install PyAudio (can be tricky)
    if check_package_installed("pyaudio"):
        print("✅ PyAudio is already installed")
        return True
    
    if system == "windows":
        # Try pip first
        if run_command("pip install pyaudio", "Installing PyAudio", ignore_errors=True):
            return True
        
        print("⚠️  PyAudio pip installation failed. Trying alternative...")
        # Try with specific wheel
        if run_command("pip install pipwin && pipwin install pyaudio", "Installing PyAudio via pipwin", ignore_errors=True):
            return True
        
        print("❌ PyAudio installation failed.")
        print("📋 Manual installation: Download wheel from https://www.lfd.uci.edu/~gohlke/pythonlibs/#pyaudio")
        return False
    
    elif system == "darwin":  # macOS
        if run_command("brew install portaudio && pip install pyaudio", "Installing PyAudio on macOS", ignore_errors=True):
            return True
        print("⚠️  Please install: brew install portaudio")
        return False
    
    else:  # Linux
        if run_command("sudo apt-get install -y portaudio19-dev && pip install pyaudio", "Installing PyAudio on Linux", ignore_errors=True):
            return True
        print("⚠️  Please install: sudo apt-get install portaudio19-dev")
        return False

def install_remaining_packages():
    """Install remaining packages"""
    remaining_packages = [
        "fastapi==0.104.1",
        "uvicorn[standard]==0.24.0",
        "python-multipart==0.0.6",
        "python-jose[cryptography]==3.3.0",
        "passlib[bcrypt]==1.7.4",
        "sqlalchemy==2.0.23",
        "databases[sqlite]==0.8.0",
        "aiosqlite==0.19.0",
        "python-dotenv==1.0.0",
        "websockets==12.0",
        "requests==2.31.0",
    ]
    
    for package in remaining_packages:
        if not run_command(f"pip install {package}", f"Installing {package}"):
            print(f"⚠️  Failed to install {package}, but continuing...")
    
    return True

def main():
    """Main installation process"""
    print("🚀 Installing Persona Audio Gatekeeper Backend Dependencies")
    print("=" * 60)
    print(f"🖥️  System: {platform.system()} {platform.release()}")
    print(f"🐍 Python: {sys.version}")
    print("=" * 60)
    
    steps = [
        ("Installing basic packages", install_basic_packages),
        ("Installing CMake", install_cmake),
        ("Installing dlib", install_dlib),
        ("Installing face_recognition", install_face_recognition),
        ("Installing OpenCV", install_opencv),
        ("Installing audio packages", install_audio_packages),
        ("Installing remaining packages", install_remaining_packages),
    ]
    
    failed_steps = []
    
    for step_name, step_func in steps:
        print(f"\n📦 {step_name}...")
        if not step_func():
            failed_steps.append(step_name)
            print(f"❌ {step_name} failed")
        else:
            print(f"✅ {step_name} completed")
    
    print("\n" + "=" * 60)
    
    if not failed_steps:
        print("🎉 All dependencies installed successfully!")
        print("\n📋 Next steps:")
        print("   1. Run: python run.py")
        print("   2. Test: python test_api.py")
        return True
    else:
        print(f"⚠️  {len(failed_steps)} step(s) failed:")
        for step in failed_steps:
            print(f"   - {step}")
        print("\n📋 You may need to install some dependencies manually.")
        print("   Check the error messages above for specific instructions.")
        return False

if __name__ == "__main__":
    success = main()
    input("\nPress Enter to continue...")
    sys.exit(0 if success else 1)
