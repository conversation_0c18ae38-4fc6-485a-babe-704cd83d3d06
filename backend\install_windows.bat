@echo off
echo 🚀 Installing Persona Audio Gatekeeper Backend Dependencies (Windows)
echo ================================================================
echo.

REM Check if virtual environment exists
if not exist "venv" (
    echo 📦 Creating virtual environment...
    python -m venv venv
    if errorlevel 1 (
        echo ❌ Failed to create virtual environment
        pause
        exit /b 1
    )
)

REM Activate virtual environment
echo 🔧 Activating virtual environment...
call venv\Scripts\activate

REM Upgrade pip first
echo 📦 Upgrading pip...
python -m pip install --upgrade pip

REM Install basic packages first
echo 📦 Installing basic packages...
pip install wheel setuptools

REM Install numpy first (required by many packages)
echo 📦 Installing numpy...
pip install numpy==1.24.3

REM Install Pillow
echo 📦 Installing Pillow...
pip install pillow==10.0.1

REM Try to install CMake
echo 📦 Installing CMake...
pip install cmake
if errorlevel 1 (
    echo ⚠️  CMake installation failed via pip
    echo 📋 Please install CMake manually from: https://cmake.org/download/
    echo    Add CMake to your PATH and restart this script
    pause
    exit /b 1
)

REM Install dlib (this is the tricky one)
echo 📦 Installing dlib (this may take several minutes)...
pip install dlib
if errorlevel 1 (
    echo ⚠️  dlib installation failed
    echo 📋 Trying alternative installation methods...
    
    REM Try with no cache
    echo 📦 Trying dlib installation without cache...
    pip install dlib --no-cache-dir
    if errorlevel 1 (
        echo ❌ dlib installation failed completely
        echo 📋 Manual installation required:
        echo    1. Install Visual Studio Build Tools
        echo    2. Or use conda: conda install -c conda-forge dlib
        echo    3. Or download precompiled wheel from:
        echo       https://github.com/sachadee/Dlib
        pause
        exit /b 1
    )
)

REM Install face_recognition
echo 📦 Installing face_recognition...
pip install face_recognition==1.3.0
if errorlevel 1 (
    echo ❌ face_recognition installation failed
    pause
    exit /b 1
)

REM Install OpenCV
echo 📦 Installing OpenCV...
pip install opencv-python==********

REM Install audio packages
echo 📦 Installing audio packages...
pip install pydub==0.25.1
pip install speechrecognition==3.10.0

REM Install PyAudio (can be problematic)
echo 📦 Installing PyAudio...
pip install pyaudio
if errorlevel 1 (
    echo ⚠️  PyAudio installation failed via pip
    echo 📦 Trying alternative method...
    pip install pipwin
    pipwin install pyaudio
    if errorlevel 1 (
        echo ❌ PyAudio installation failed
        echo 📋 Please download PyAudio wheel from:
        echo    https://www.lfd.uci.edu/~gohlke/pythonlibs/#pyaudio
        echo    Then install with: pip install downloaded_wheel.whl
    )
)

REM Install FastAPI and other web packages
echo 📦 Installing FastAPI and web packages...
pip install fastapi==0.104.1
pip install uvicorn[standard]==0.24.0
pip install python-multipart==0.0.6

REM Install security packages
echo 📦 Installing security packages...
pip install python-jose[cryptography]==3.3.0
pip install passlib[bcrypt]==1.7.4

REM Install database packages
echo 📦 Installing database packages...
pip install sqlalchemy==2.0.23
pip install databases[sqlite]==0.8.0
pip install aiosqlite==0.19.0

REM Install utility packages
echo 📦 Installing utility packages...
pip install python-dotenv==1.0.0
pip install websockets==12.0
pip install requests==2.31.0

echo.
echo ================================================================
echo 🎉 Installation completed!
echo.
echo 📋 Next steps:
echo    1. Copy .env.example to .env and configure
echo    2. Run: python run.py
echo    3. Test: python test_api.py
echo    4. Visit: http://localhost:8000/docs
echo.
echo 🔍 If you encountered any errors, check the messages above
echo    and install missing dependencies manually.
echo.
pause
