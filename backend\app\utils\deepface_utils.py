from deepface import DeepFace
import cv2
import numpy as np
import base64
import os

def read_image_from_base64(base64_string):
    """Converts base64 string to OpenCV image"""
    img_data = base64.b64decode(base64_string)
    nparr = np.frombuffer(img_data, np.uint8)
    return cv2.imdecode(nparr, cv2.IMREAD_COLOR)

def enroll_face(base64_img, username):
    """Enrolls a user's face by saving it to the registered_faces directory"""
    try:
        # Create registered_faces directory if it doesn't exist
        os.makedirs("registered_faces", exist_ok=True)

        # Convert base64 to image
        img = read_image_from_base64(base64_img)

        # Save the image
        image_path = f"registered_faces/{username}.jpg"
        success = cv2.imwrite(image_path, img)

        if success:
            print(f"✅ Face enrolled for {username} at {image_path}")
            return True
        else:
            print(f"❌ Failed to save face image for {username}")
            return False

    except Exception as e:
        print(f"❌ Face enrollment error for {username}: {e}")
        return False

def verify_face(base64_img, registered_img_path):
    """Verifies if the input face matches the registered one."""
    try:
        img = read_image_from_base64(base64_img)
        result = DeepFace.verify(img1_path=img, img2_path=registered_img_path, enforce_detection=False)
        return result['verified']
    except Exception as e:
        print(f"DeepFace verification error: {e}")
        return False

def get_face_confidence(base64_img, registered_img_path):
    """Gets the confidence score for face verification"""
    try:
        img = read_image_from_base64(base64_img)
        result = DeepFace.verify(img1_path=img, img2_path=registered_img_path, enforce_detection=False)

        # DeepFace returns distance, convert to confidence (0-1)
        distance = result.get('distance', 1.0)
        confidence = max(0.0, 1.0 - distance)

        return round(confidence, 3)
    except Exception as e:
        print(f"DeepFace confidence error: {e}")
        return 0.0
