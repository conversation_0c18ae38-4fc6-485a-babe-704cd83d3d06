from deepface import DeepFace
import cv2
import numpy as np
import base64
import os

def read_image_from_base64(base64_string):
    """Converts base64 string to OpenCV image"""
    img_data = base64.b64decode(base64_string)
    nparr = np.frombuffer(img_data, np.uint8)
    return cv2.imdecode(nparr, cv2.IMREAD_COLOR)

def verify_face(base64_img, registered_img_path):
    """Verifies if the input face matches the registered one."""
    try:
        img = read_image_from_base64(base64_img)
        result = DeepFace.verify(img1_path=img, img2_path=registered_img_path, enforce_detection=False)
        return result['verified']
    except Exception as e:
        print(f"DeepFace error: {e}")
        return False
