from fastapi import <PERSON><PERSON><PERSON>, HTTPException, UploadFile, File, Form
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
import os
from dotenv import load_dotenv

from app.routers import auth, audio_recognition, biometric
from app.database import database, engine, metadata
from app.models import users
from app.config import USE_FACE_RECOGNITION_LIB, ALLOWED_ORIGINS, API_HOST, API_PORT, DEBUG

# Load environment variables
load_dotenv()

# Import appropriate face recognition module
if USE_FACE_RECOGNITION_LIB:
    from app.routers import face_recognition
    print("🎭 Using face_recognition library for face detection")
else:
    from app.routers import face_recognition_opencv as face_recognition
    print("🎭 Using OpenCV fallback for face detection")

# Create FastAPI app
app = FastAPI(
    title="Persona Audio Gatekeeper API",
    description="Backend API for face and audio recognition authentication",
    version="1.0.0"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Create tables
metadata.create_all(bind=engine)

# Database connection events
@app.on_event("startup")
async def startup():
    await database.connect()

@app.on_event("shutdown")
async def shutdown():
    await database.disconnect()

# Include routers
app.include_router(auth.router, prefix="/api/auth", tags=["authentication"])
app.include_router(face_recognition.router, prefix="/api/face", tags=["face-recognition"])
app.include_router(audio_recognition.router, prefix="/api/audio", tags=["audio-recognition"])
app.include_router(biometric.router, prefix="/api/biometric", tags=["biometric-authentication"])

@app.get("/")
async def root():
    return {"message": "Persona Audio Gatekeeper API is running"}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "database": "connected"}

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True
    )
