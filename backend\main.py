from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from dotenv import load_dotenv
import uvicorn

from app.database import database, metadata, engine
from app.routers import auth, audio_recognition, biometric, face_recognition_deepface
from fastapi import APIRouter


# Load environment variables
load_dotenv()

# FastAPI app setup
app = FastAPI(
    title="Persona Audio Gatekeeper API",
    description="Backend API for face (DeepFace) and audio recognition authentication",
    version="1.0.0"
)

# CORS settings
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # You can restrict this in production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Create database tables (commented out to preserve existing data)
# metadata.create_all(bind=engine)

# Connect/disconnect events
@app.on_event("startup")
async def startup():
    await database.connect()

@app.on_event("shutdown")
async def shutdown():
    await database.disconnect()

# Include all routes
app.include_router(auth.router, prefix="/api/auth", tags=["Authentication"])
app.include_router(face_recognition_deepface.router, prefix="/api/face", tags=["Face Recognition (DeepFace)"])
app.include_router(audio_recognition.router, prefix="/api/audio", tags=["Audio Recognition"])
app.include_router(biometric.router, prefix="/api/biometric", tags=["Combined Biometric Authentication"])

# Health check and root
@app.get("/")
async def root():
    return {"message": "Persona Audio Gatekeeper API is running"}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "database": "connected"}

# Development server entry
if __name__ == "__main__":
    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True)

router = APIRouter()
