#!/usr/bin/env python3
"""
Simple API test script for Persona Audio Gatekeeper Backend
Tests basic functionality of the API endpoints
"""

import requests
import json
import sys
import time

BASE_URL = "http://localhost:8000"

def test_health():
    """Test health endpoint"""
    try:
        response = requests.get(f"{BASE_URL}/health")
        if response.status_code == 200:
            print("✅ Health check passed")
            return True
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to server. Is it running?")
        return False

def test_register_user():
    """Test user registration"""
    try:
        user_data = {
            "username": "test_user",
            "email": "<EMAIL>"
        }
        response = requests.post(f"{BASE_URL}/api/auth/register", json=user_data)
        if response.status_code == 200:
            print("✅ User registration passed")
            return True
        elif response.status_code == 400 and "already registered" in response.text:
            print("✅ User already exists (expected)")
            return True
        else:
            print(f"❌ User registration failed: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ User registration error: {e}")
        return False

def test_get_user():
    """Test get user endpoint"""
    try:
        response = requests.get(f"{BASE_URL}/api/auth/user/test_user")
        if response.status_code == 200:
            print("✅ Get user passed")
            return True
        else:
            print(f"❌ Get user failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Get user error: {e}")
        return False

def test_biometric_status():
    """Test biometric status endpoint"""
    try:
        response = requests.get(f"{BASE_URL}/api/biometric/status/test_user")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Biometric status: Face enrolled: {data.get('face_enrolled')}, Voice enrolled: {data.get('voice_enrolled')}")
            return True
        else:
            print(f"❌ Biometric status failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Biometric status error: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 Testing Persona Audio Gatekeeper Backend API...")
    print("=" * 50)
    
    tests = [
        ("Health Check", test_health),
        ("User Registration", test_register_user),
        ("Get User", test_get_user),
        ("Biometric Status", test_biometric_status),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 Running {test_name}...")
        if test_func():
            passed += 1
        time.sleep(0.5)  # Small delay between tests
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Backend is working correctly.")
        return 0
    else:
        print("⚠️  Some tests failed. Check the output above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
