from fastapi import APIRouter, HTTPException, UploadFile, File, Form, Request
import speech_recognition as sr
import io
import json
import numpy as np
from pydub import AudioSegment
from pydub.utils import make_chunks
import hashlib
from app.database import database
from app.models import users
from app.schemas import AudioRecognitionResponse, EnrollmentResponse
from app.routers.auth import log_authentication
from sqlalchemy import select, update

router = APIRouter()

def process_audio_data(audio_data: bytes) -> tuple:
    """Process audio data and extract features"""
    try:
        # Convert audio to AudioSegment
        audio = AudioSegment.from_file(io.BytesIO(audio_data))
        
        # Convert to mono and standard sample rate
        audio = audio.set_channels(1).set_frame_rate(16000)
        
        # Extract basic audio features for voice recognition
        # This is a simplified approach - in production, you'd use more sophisticated methods
        
        # Get audio properties
        duration = len(audio) / 1000.0  # Duration in seconds
        max_amplitude = audio.max
        rms = audio.rms
        
        # Create chunks and analyze
        chunk_length_ms = 100  # 100ms chunks
        chunks = make_chunks(audio, chunk_length_ms)
        
        # Calculate features
        amplitudes = [chunk.max for chunk in chunks if len(chunk) > 0]
        rms_values = [chunk.rms for chunk in chunks if len(chunk) > 0]
        
        # Create a simple voice print (in production, use MFCC or other advanced features)
        voice_features = {
            'duration': duration,
            'max_amplitude': max_amplitude,
            'avg_amplitude': np.mean(amplitudes) if amplitudes else 0,
            'rms': rms,
            'avg_rms': np.mean(rms_values) if rms_values else 0,
            'amplitude_variance': np.var(amplitudes) if amplitudes else 0,
            'rms_variance': np.var(rms_values) if rms_values else 0,
        }
        
        # Convert audio to wav format for speech recognition
        wav_data = io.BytesIO()
        audio.export(wav_data, format="wav")
        wav_data.seek(0)
        
        return voice_features, wav_data
    
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Invalid audio format: {str(e)}")

def extract_speech_text(wav_data: io.BytesIO) -> str:
    """Extract text from audio using speech recognition"""
    try:
        recognizer = sr.Recognizer()
        
        with sr.AudioFile(wav_data) as source:
            audio = recognizer.record(source)
            
        # Use Google Speech Recognition (free tier)
        text = recognizer.recognize_google(audio)
        return text.lower().strip()
    
    except sr.UnknownValueError:
        return ""
    except sr.RequestError as e:
        raise HTTPException(status_code=500, detail=f"Speech recognition error: {str(e)}")

def calculate_voice_similarity(features1: dict, features2: dict) -> float:
    """Calculate similarity between two voice feature sets"""
    try:
        # Simple similarity calculation based on feature differences
        # In production, use more sophisticated voice comparison algorithms
        
        weights = {
            'max_amplitude': 0.2,
            'avg_amplitude': 0.2,
            'rms': 0.2,
            'avg_rms': 0.2,
            'amplitude_variance': 0.1,
            'rms_variance': 0.1
        }
        
        total_similarity = 0
        total_weight = 0
        
        for feature, weight in weights.items():
            if feature in features1 and feature in features2:
                val1 = features1[feature]
                val2 = features2[feature]
                
                if val1 == 0 and val2 == 0:
                    similarity = 1.0
                elif val1 == 0 or val2 == 0:
                    similarity = 0.0
                else:
                    # Calculate normalized difference
                    diff = abs(val1 - val2) / max(abs(val1), abs(val2))
                    similarity = max(0, 1 - diff)
                
                total_similarity += similarity * weight
                total_weight += weight
        
        return total_similarity / total_weight if total_weight > 0 else 0.0
    
    except Exception:
        return 0.0

@router.post("/enroll", response_model=EnrollmentResponse)
async def enroll_voice(
    username: str = Form(...),
    audio: UploadFile = File(...)
):
    """Enroll a user's voice for recognition"""
    try:
        # Check if user exists
        query = select(users).where(users.c.username == username)
        user = await database.fetch_one(query)
        
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        # Read and process audio
        audio_data = await audio.read()
        voice_features, wav_data = process_audio_data(audio_data)
        
        # Extract speech text for additional verification
        speech_text = extract_speech_text(wav_data)
        
        # Create voice print
        voice_print = {
            'features': voice_features,
            'sample_text': speech_text,
            'enrollment_phrase': speech_text  # Store the enrollment phrase
        }
        
        # Store voice print in database
        voice_print_json = json.dumps(voice_print)
        
        query = update(users).where(users.c.id == user.id).values(
            voice_print=voice_print_json
        )
        await database.execute(query)
        
        return EnrollmentResponse(
            success=True,
            message=f"Voice enrolled successfully. Detected phrase: '{speech_text}'",
            user_id=user.id
        )
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Voice enrollment failed: {str(e)}")

@router.post("/verify", response_model=AudioRecognitionResponse)
async def verify_voice(
    username: str = Form(...),
    audio: UploadFile = File(...),
    request: Request = None
):
    """Verify a user's voice"""
    try:
        # Get user and their stored voice print
        query = select(users).where(users.c.username == username)
        user = await database.fetch_one(query)
        
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        if not user.voice_print:
            return AudioRecognitionResponse(
                success=False,
                message="No voice data enrolled for this user"
            )
        
        # Read and process audio
        audio_data = await audio.read()
        voice_features, wav_data = process_audio_data(audio_data)
        
        # Extract speech text
        speech_text = extract_speech_text(wav_data)
        
        # Load stored voice print
        stored_voice_print = json.loads(user.voice_print)
        stored_features = stored_voice_print['features']
        
        # Calculate voice similarity
        voice_similarity = calculate_voice_similarity(voice_features, stored_features)
        
        # Text similarity (optional - can be used for additional security)
        text_similarity = 1.0  # For now, we'll focus on voice characteristics
        
        # Combined confidence score
        confidence = (voice_similarity * 0.8 + text_similarity * 0.2)
        
        # Threshold for voice recognition (adjust as needed)
        threshold = 0.6
        success = confidence >= threshold
        
        # Log authentication attempt
        await log_authentication(
            user.id, username, "voice", success, confidence,
            request.client.host if request else None,
            request.headers.get("user-agent") if request else None
        )
        
        message = "Voice verified successfully" if success else "Voice verification failed"
        if speech_text:
            message += f" (Detected: '{speech_text}')"
        
        return AudioRecognitionResponse(
            success=success,
            message=message,
            confidence=confidence,
            user_id=user.id if success else None
        )
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Voice verification failed: {str(e)}")

@router.post("/analyze")
async def analyze_audio(audio: UploadFile = File(...)):
    """Analyze audio file and return basic information"""
    try:
        audio_data = await audio.read()
        voice_features, wav_data = process_audio_data(audio_data)
        
        # Extract speech text
        speech_text = extract_speech_text(wav_data)
        
        return {
            "duration": voice_features.get('duration', 0),
            "detected_speech": speech_text,
            "audio_quality": "good" if voice_features.get('rms', 0) > 100 else "low",
            "message": f"Audio analyzed successfully. Duration: {voice_features.get('duration', 0):.2f}s"
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Audio analysis failed: {str(e)}")
