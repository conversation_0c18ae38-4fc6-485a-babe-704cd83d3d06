# 🎯 Persona Audio Gatekeeper - Team Setup Guide

## 🚀 Quick Start (Recommended)

### Option 1: Windows Users
```bash
# Double-click this file to start both servers
start_system.bat
```

### Option 2: All Platforms
```bash
# Run the Python start script
python start_system.py
```

### Option 3: Manual Start
```bash
# Terminal 1: Start Backend
cd backend
venv\Scripts\python.exe run.py  # Windows
# OR
venv/bin/python run.py          # Mac/Linux

# Terminal 2: Start Frontend
npm run dev
```

---

## 👥 Team Member Authentication

### Your Team Usernames:
- **team_member_1** (Team Member 1)
- **team_member_2** (Team Member 2)  
- **team_member_3** (Team Member 3)

### 🔐 Authentication Process:

1. **Open the App**: Go to http://localhost:5173
2. **Select Username**: Click "Show authorized team members" and select your username
3. **Login**: Click "Proceed to Verification"
4. **Face Enrollment**: Capture your face (first time only)
5. **Voice Enrollment**: Record your voice (first time only)
6. **Authentication**: Use face and voice to verify your identity

---

## 🌐 System URLs

| Service | URL | Description |
|---------|-----|-------------|
| **Frontend** | http://localhost:5173 | Main application interface |
| **Backend API** | http://127.0.0.1:8000 | REST API server |
| **API Documentation** | http://127.0.0.1:8000/docs | Interactive API docs |
| **Health Check** | http://127.0.0.1:8000/health | Server status |
| **User Status** | http://127.0.0.1:8000/api/auth/authorized-users | Team enrollment status |

---

## 📋 Backend API Endpoints

### Authentication Routes (`/api/auth/`)
- `POST /api/auth/register` - Register team member
- `POST /api/auth/login` - Login team member
- `GET /api/auth/user/{username}` - Get user details
- `GET /api/auth/authorized-users` - List all team members and status

### Face Recognition Routes (`/api/face/`)
- `POST /api/face/enroll` - Enroll face biometric
- `POST /api/face/verify-face` - Verify face authentication

### Voice Recognition Routes (`/api/biometric/`)
- `POST /api/biometric/enroll-voice` - Enroll voice biometric
- `POST /api/biometric/verify-voice` - Verify voice authentication

---

## 🔧 Technical Architecture

### Backend Components:
- **FastAPI** - REST API framework
- **DeepFace** - Face recognition using deep learning
- **MFCC + Cosine Similarity** - Voice recognition
- **SQLite** - Database for user data and logs
- **Pydantic** - Data validation and serialization

### Frontend Components:
- **React + TypeScript** - UI framework
- **Framer Motion** - Animations
- **Radix UI** - Component library
- **TailwindCSS** - Styling
- **MediaRecorder API** - Camera and microphone access

### Database Schema:
```sql
-- Users table
users (
    id INTEGER PRIMARY KEY,
    username VARCHAR(50) UNIQUE,
    email VARCHAR(100),
    face_encoding TEXT,      -- Face biometric data
    voice_print TEXT,        -- Voice biometric data
    is_active BOOLEAN,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
)

-- Authentication logs
authentication_logs (
    id INTEGER PRIMARY KEY,
    user_id INTEGER,
    username VARCHAR(50),
    auth_type VARCHAR(20),   -- 'face', 'voice', 'combined'
    success BOOLEAN,
    confidence_score VARCHAR(10),
    ip_address VARCHAR(45),
    user_agent VARCHAR(500),
    timestamp TIMESTAMP
)
```

---

## 🛠️ Troubleshooting

### Backend Issues:
```bash
# Check if backend is running
curl http://127.0.0.1:8000/health

# View backend logs
cd backend
venv\Scripts\python.exe run.py

# Reinstall dependencies
cd backend
venv\Scripts\pip.exe install -r requirements.txt
```

### Frontend Issues:
```bash
# Check if frontend is running
curl http://localhost:5173

# Reinstall dependencies
npm install

# Clear cache and restart
npm run dev
```

### Database Issues:
```bash
# Reinitialize database
python setup_team_authentication.py

# Check database contents
sqlite3 backend/db.sqlite3
.tables
SELECT * FROM users;
```

---

## 📊 Monitoring Team Status

Check team enrollment status:
```bash
curl http://127.0.0.1:8000/api/auth/authorized-users
```

Response shows each team member's enrollment status:
```json
{
  "success": true,
  "authorized_users": [
    {
      "username": "team_member_1",
      "registered": true,
      "has_face": true,
      "has_voice": true,
      "ready_for_auth": true
    }
  ],
  "total_users": 3,
  "registered_users": 3,
  "ready_users": 1
}
```

---

## 🔒 Security Features

- **Authorized Users Only**: Only 3 predefined team members can access
- **Biometric Authentication**: Face + Voice verification required
- **Confidence Scoring**: Each authentication attempt scored for accuracy
- **Audit Logging**: All authentication attempts logged with timestamps
- **Session Management**: Secure user sessions with proper validation

---

## 📁 Project Structure

```
persona-audio-gatekeeper/
├── backend/                 # Python FastAPI backend
│   ├── app/
│   │   ├── routers/        # API endpoints
│   │   ├── utils/          # DeepFace & audio utilities
│   │   ├── models.py       # Database models
│   │   └── database.py     # Database configuration
│   ├── registered_faces/   # Stored face images
│   ├── venv/              # Python virtual environment
│   └── requirements.txt   # Python dependencies
├── src/                   # React frontend
│   ├── components/        # UI components
│   ├── services/         # API service layer
│   └── hooks/            # Custom React hooks
├── setup_team_authentication.py  # Setup script
├── start_system.py       # Start script (Python)
└── start_system.bat      # Start script (Windows)
```

---

## ✅ Success Checklist

- [ ] Backend server running on http://127.0.0.1:8000
- [ ] Frontend app running on http://localhost:5173
- [ ] All 3 team members can login
- [ ] Face enrollment working for each member
- [ ] Voice enrollment working for each member
- [ ] Face verification working with confidence scores
- [ ] Voice verification working with confidence scores
- [ ] Authentication logs being recorded

**🎉 Your biometric authentication system is ready!**
