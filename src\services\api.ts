/**
 * API Service for Persona Audio Gatekeeper
 * Handles all backend communication
 */

const API_BASE_URL = 'http://127.0.0.1:8000/api';

// Types for API responses
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
}

export interface UserRegisterRequest {
  username: string;
  email?: string;
}

export interface UserLoginRequest {
  username: string;
}

export interface UserResponse {
  user_id: number;
  username: string;
  email?: string;
  is_active: boolean;
  has_face: boolean;
  has_voice: boolean;
  created_at: string;
}

export interface AuthResponse {
  success: boolean;
  message: string;
  user_id?: number;
  username?: string;
  has_face?: boolean;
  has_voice?: boolean;
}

export interface VerificationResponse {
  success: boolean;
  message: string;
  confidence?: number;
  user_id?: number;
}

export interface FaceVerifyRequest {
  base64_image: string;
  username: string;
}

// Utility function to handle API errors
const handleApiError = (error: any): never => {
  if (error.response?.data?.detail) {
    throw new Error(error.response.data.detail);
  } else if (error.message) {
    throw new Error(error.message);
  } else {
    throw new Error('An unexpected error occurred');
  }
};

// Utility function to convert blob to base64
export const blobToBase64 = (blob: Blob): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onloadend = () => {
      const result = reader.result as string;
      // Remove data URL prefix to get just the base64 string
      const base64 = result.split(',')[1];
      resolve(base64);
    };
    reader.onerror = reject;
    reader.readAsDataURL(blob);
  });
};

// Utility function to capture image from video element
export const captureImageFromVideo = (videoElement: HTMLVideoElement): Promise<Blob> => {
  return new Promise((resolve) => {
    const canvas = document.createElement('canvas');
    canvas.width = videoElement.videoWidth;
    canvas.height = videoElement.videoHeight;
    
    const ctx = canvas.getContext('2d');
    if (ctx) {
      ctx.drawImage(videoElement, 0, 0);
      canvas.toBlob((blob) => {
        resolve(blob!);
      }, 'image/jpeg', 0.8);
    }
  });
};

// API Service Class
class ApiService {
  private baseUrl: string;

  constructor(baseUrl: string = API_BASE_URL) {
    this.baseUrl = baseUrl;
  }

  // Generic fetch wrapper
  private async fetchApi<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    try {
      const response = await fetch(`${this.baseUrl}${endpoint}`, {
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
        ...options,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || `HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      handleApiError(error);
    }
  }

  // Generic FormData fetch wrapper
  private async fetchFormData<T>(
    endpoint: string,
    formData: FormData,
    options: RequestInit = {}
  ): Promise<T> {
    try {
      const response = await fetch(`${this.baseUrl}${endpoint}`, {
        method: 'POST',
        body: formData,
        ...options,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || `HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      handleApiError(error);
    }
  }

  // Health check
  async healthCheck(): Promise<{ status: string; database: string }> {
    return this.fetchApi('/health');
  }

  // Authentication endpoints
  async registerUser(userData: UserRegisterRequest): Promise<AuthResponse> {
    return this.fetchApi('/auth/register', {
      method: 'POST',
      body: JSON.stringify(userData),
    });
  }

  async loginUser(userData: UserLoginRequest): Promise<AuthResponse> {
    return this.fetchApi('/auth/login', {
      method: 'POST',
      body: JSON.stringify(userData),
    });
  }

  async getUserInfo(username: string): Promise<UserResponse> {
    return this.fetchApi(`/auth/user/${username}`);
  }

  // Face recognition endpoints
  async verifyFace(username: string, base64Image: string): Promise<VerificationResponse> {
    const data: FaceVerifyRequest = {
      base64_image: base64Image,
      username: username,
    };

    return this.fetchApi('/face/verify-face', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async enrollFace(username: string, imageBlob: Blob): Promise<VerificationResponse> {
    const base64Image = await blobToBase64(imageBlob);
    
    // For enrollment, we need to save the image on the backend
    // This might require a separate endpoint or modification of the verify endpoint
    const formData = new FormData();
    formData.append('username', username);
    formData.append('image', imageBlob, 'face.jpg');

    return this.fetchFormData('/face/enroll', formData);
  }

  // Voice recognition endpoints
  async enrollVoice(username: string, audioBlob: Blob): Promise<VerificationResponse> {
    const formData = new FormData();
    formData.append('username', username);
    formData.append('audio', audioBlob, 'voice.wav');

    return this.fetchFormData('/biometric/enroll-voice', formData);
  }

  async verifyVoice(username: string, audioBlob: Blob): Promise<VerificationResponse> {
    const formData = new FormData();
    formData.append('username', username);
    formData.append('audio', audioBlob, 'voice.wav');

    return this.fetchFormData('/biometric/verify-voice', formData);
  }

  // Combined biometric authentication
  async authenticateBiometric(
    username: string,
    faceBlob?: Blob,
    voiceBlob?: Blob
  ): Promise<{
    face_result?: VerificationResponse;
    voice_result?: VerificationResponse;
    overall_success: boolean;
    message: string;
  }> {
    const results: any = {
      overall_success: false,
      message: 'Authentication failed',
    };

    try {
      // Verify face if provided
      if (faceBlob) {
        const base64Image = await blobToBase64(faceBlob);
        results.face_result = await this.verifyFace(username, base64Image);
      }

      // Verify voice if provided
      if (voiceBlob) {
        results.voice_result = await this.verifyVoice(username, voiceBlob);
      }

      // Determine overall success
      const faceSuccess = !faceBlob || (results.face_result?.success ?? false);
      const voiceSuccess = !voiceBlob || (results.voice_result?.success ?? false);
      
      results.overall_success = faceSuccess && voiceSuccess;
      results.message = results.overall_success 
        ? 'Authentication successful' 
        : 'Authentication failed';

      return results;
    } catch (error) {
      results.message = error instanceof Error ? error.message : 'Authentication error';
      return results;
    }
  }
}

// Export singleton instance
export const apiService = new ApiService();

// Export individual functions for convenience
export const {
  healthCheck,
  registerUser,
  loginUser,
  getUserInfo,
  verifyFace,
  enrollFace,
  enrollVoice,
  verifyVoice,
  authenticateBiometric,
} = apiService;
