#!/usr/bin/env python3
"""
Simple server startup script
"""

import sys
import os

# Add current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    print("🚀 Starting Persona Audio Gatekeeper Backend...")
    print("📡 Server will be available at: http://127.0.0.1:8000")
    
    import uvicorn
    uvicorn.run(
        "main:app",
        host="127.0.0.1",
        port=8000,
        reload=True,
        log_level="info"
    )
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Please install dependencies: pip install -r requirements.txt")
except Exception as e:
    print(f"❌ Server error: {e}")
    import traceback
    traceback.print_exc()
